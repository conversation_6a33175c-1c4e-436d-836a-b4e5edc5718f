AM_CPPFLAGS += -I$(top_builddir) -I$(top_srcdir)
noinst_LTLIBRARIES = libwebpdecode.la

libwebpdecode_la_SOURCES =
libwebpdecode_la_SOURCES += alpha_dec.c
libwebpdecode_la_SOURCES += alphai_dec.h
libwebpdecode_la_SOURCES += buffer_dec.c
libwebpdecode_la_SOURCES += common_dec.h
libwebpdecode_la_SOURCES += vp8_dec.h
libwebpdecode_la_SOURCES += frame_dec.c
libwebpdecode_la_SOURCES += idec_dec.c
libwebpdecode_la_SOURCES += io_dec.c
libwebpdecode_la_SOURCES += quant_dec.c
libwebpdecode_la_SOURCES += tree_dec.c
libwebpdecode_la_SOURCES += vp8_dec.c
libwebpdecode_la_SOURCES += vp8i_dec.h
libwebpdecode_la_SOURCES += vp8l_dec.c
libwebpdecode_la_SOURCES += vp8li_dec.h
libwebpdecode_la_SOURCES += webp_dec.c
libwebpdecode_la_SOURCES += webpi_dec.h

libwebpdecodeinclude_HEADERS =
libwebpdecodeinclude_HEADERS += ../webp/decode.h
libwebpdecodeinclude_HEADERS += ../webp/types.h
noinst_HEADERS =
noinst_HEADERS += ../webp/format_constants.h

libwebpdecode_la_CPPFLAGS = $(AM_CPPFLAGS)
libwebpdecodeincludedir = $(includedir)/webp

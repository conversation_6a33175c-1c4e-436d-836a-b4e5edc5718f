#ifdef __OBJC__
#import <UIKit/UIKit.h>
#else
#ifndef FOUNDATION_EXPORT
#if defined(__cplusplus)
#define FOUNDATION_EXPORT extern "C"
#else
#define FOUNDATION_EXPORT extern
#endif
#endif
#endif

#import "MetalContext.h"
#import "MetalWindowContext.h"
#import "RNSkApplePlatformContext.h"
#import "RNSkAppleVideo.h"
#import "RNSkAppleView.h"
#import "RNSkiaModule.h"
#import "RNSkMetalCanvasProvider.h"
#import "SkiaCVPixelBufferUtils.h"
#import "SkiaManager.h"
#import "SkiaPictureView.h"
#import "SkiaPictureViewManager.h"
#import "SkiaUIView.h"
#import "ViewScreenshotService.h"
#import "JsiNativeBuffer.h"
#import "JsiSkAnimatedImage.h"
#import "JsiSkAnimatedImageFactory.h"
#import "JsiSkApi.h"
#import "JsiSkCanvas.h"
#import "JsiSkColor.h"
#import "JsiSkColorFilter.h"
#import "JsiSkColorFilterFactory.h"
#import "JsiSkContourMeasure.h"
#import "JsiSkContourMeasureIter.h"
#import "JsiSkData.h"
#import "JsiSkDataFactory.h"
#import "JsiSkFont.h"
#import "JsiSkFontMgr.h"
#import "JsiSkFontMgrFactory.h"
#import "JsiSkFontStyle.h"
#import "JsiSkHostObjects.h"
#import "JsiSkiaContext.h"
#import "JsiSkImage.h"
#import "JsiSkImageFactory.h"
#import "JsiSkImageFilter.h"
#import "JsiSkImageFilterFactory.h"
#import "JsiSkImageInfo.h"
#import "JsiSkMaskFilter.h"
#import "JsiSkMaskFilterFactory.h"
#import "JsiSkMatrix.h"
#import "JsiSkPaint.h"
#import "JsiSkParagraph.h"
#import "JsiSkParagraphBuilder.h"
#import "JsiSkParagraphBuilderFactory.h"
#import "JsiSkParagraphStyle.h"
#import "JsiSkPath.h"
#import "JsiSkPathEffect.h"
#import "JsiSkPathEffectFactory.h"
#import "JsiSkPathFactory.h"
#import "JsiSkPicture.h"
#import "JsiSkPictureFactory.h"
#import "JsiSkPictureRecorder.h"
#import "JsiSkPoint.h"
#import "JsiSkRect.h"
#import "JsiSkRRect.h"
#import "JsiSkRSXform.h"
#import "JsiSkRuntimeEffect.h"
#import "JsiSkRuntimeEffectFactory.h"
#import "JsiSkRuntimeShaderBuilder.h"
#import "JsiSkShader.h"
#import "JsiSkShaderFactory.h"
#import "JsiSkStrutStyle.h"
#import "JsiSkSurface.h"
#import "JsiSkSurfaceFactory.h"
#import "JsiSkSVG.h"
#import "JsiSkSVGFactory.h"
#import "JsiSkTextBlob.h"
#import "JsiSkTextBlobFactory.h"
#import "JsiSkTextStyle.h"
#import "JsiSkTypeface.h"
#import "JsiSkTypefaceFactory.h"
#import "JsiSkTypefaceFontProvider.h"
#import "JsiSkTypefaceFontProviderFactory.h"
#import "JsiSkVertices.h"
#import "JsiTextureInfo.h"
#import "JsiVideo.h"
#import "ColorFilters.h"
#import "Command.h"
#import "Convertor.h"
#import "DataTypes.h"
#import "DrawingCtx.h"
#import "Drawings.h"
#import "ImageFilters.h"
#import "ImageFit.h"
#import "JsiRecorder.h"
#import "Paint.h"
#import "PathEffects.h"
#import "RNRecorder.h"
#import "Shaders.h"
#import "base64.h"
#import "CSSColorParser.h"
#import "JsiHostObject.h"
#import "JsiPromises.h"
#import "RuntimeAwareCache.h"
#import "RuntimeLifecycleMonitor.h"
#import "ViewProperty.h"
#import "RNSkJsiViewApi.h"
#import "RNSkManager.h"
#import "RNSkPictureView.h"
#import "RNSkPlatformContext.h"
#import "RNSkVideo.h"
#import "RNSkView.h"
#import "WindowContext.h"
#import "AHardwareBufferUtils.h"
#import "GrAHardwareBufferUtils.h"
#import "SurfaceAndroid.h"
#import "SkAndroidFrameworkUtils.h"
#import "SkAnimatedImage.h"
#import "SkCanvasAndroid.h"
#import "SkHeifDecoder.h"
#import "SkImageAndroid.h"
#import "SkSurfaceAndroid.h"
#import "SkAndroidCodec.h"
#import "SkAvifDecoder.h"
#import "SkBmpDecoder.h"
#import "SkCodec.h"
#import "SkCodecAnimation.h"
#import "SkEncodedImageFormat.h"
#import "SkEncodedOrigin.h"
#import "SkGifDecoder.h"
#import "SkIcoDecoder.h"
#import "SkJpegDecoder.h"
#import "SkJpegxlDecoder.h"
#import "SkPixmapUtils.h"
#import "SkPngChunkReader.h"
#import "SkPngDecoder.h"
#import "SkRawDecoder.h"
#import "SkWbmpDecoder.h"
#import "SkWebpDecoder.h"
#import "SkUserConfig.h"
#import "SkAlphaType.h"
#import "SkAnnotation.h"
#import "SkArc.h"
#import "SkBBHFactory.h"
#import "SkBitmap.h"
#import "SkBlender.h"
#import "SkBlendMode.h"
#import "SkBlurTypes.h"
#import "SkCanvas.h"
#import "SkCanvasVirtualEnforcer.h"
#import "SkCapabilities.h"
#import "SkClipOp.h"
#import "SkColor.h"
#import "SkColorFilter.h"
#import "SkColorSpace.h"
#import "SkColorTable.h"
#import "SkColorType.h"
#import "SkContourMeasure.h"
#import "SkCoverageMode.h"
#import "SkCubicMap.h"
#import "SkData.h"
#import "SkDataTable.h"
#import "SkDocument.h"
#import "SkDrawable.h"
#import "SkExecutor.h"
#import "SkFlattenable.h"
#import "SkFont.h"
#import "SkFontArguments.h"
#import "SkFontMetrics.h"
#import "SkFontMgr.h"
#import "SkFontParameters.h"
#import "SkFontScanner.h"
#import "SkFontStyle.h"
#import "SkFontTypes.h"
#import "SkFourByteTag.h"
#import "SkGraphics.h"
#import "SkImage.h"
#import "SkImageFilter.h"
#import "SkImageGenerator.h"
#import "SkImageInfo.h"
#import "SkM44.h"
#import "SkMallocPixelRef.h"
#import "SkMaskFilter.h"
#import "SkMatrix.h"
#import "SkMesh.h"
#import "SkMilestone.h"
#import "SkOpenTypeSVGDecoder.h"
#import "SkOverdrawCanvas.h"
#import "SkPaint.h"
#import "SkPath.h"
#import "SkPathBuilder.h"
#import "SkPathEffect.h"
#import "SkPathMeasure.h"
#import "SkPathTypes.h"
#import "SkPathUtils.h"
#import "SkPicture.h"
#import "SkPictureRecorder.h"
#import "SkPixelRef.h"
#import "SkPixmap.h"
#import "SkPoint.h"
#import "SkPoint3.h"
#import "SkRasterHandleAllocator.h"
#import "SkRect.h"
#import "SkRefCnt.h"
#import "SkRegion.h"
#import "SkRRect.h"
#import "SkRSXform.h"
#import "SkSamplingOptions.h"
#import "SkScalar.h"
#import "SkSerialProcs.h"
#import "SkShader.h"
#import "SkSize.h"
#import "SkSpan.h"
#import "SkStream.h"
#import "SkString.h"
#import "SkStrokeRec.h"
#import "SkSurface.h"
#import "SkSurfaceProps.h"
#import "SkSwizzle.h"
#import "SkTextBlob.h"
#import "SkTextureCompressionType.h"
#import "SkTiledImageUtils.h"
#import "SkTileMode.h"
#import "SkTraceMemoryDump.h"
#import "SkTypeface.h"
#import "SkTypes.h"
#import "SkUnPreMultiply.h"
#import "SkVertices.h"
#import "SkYUVAInfo.h"
#import "SkYUVAPixmaps.h"
#import "SkMultiPictureDocument.h"
#import "SkPDFDocument.h"
#import "SkPDFJpegHelpers.h"
#import "SkXPSDocument.h"
#import "Sk1DPathEffect.h"
#import "Sk2DPathEffect.h"
#import "SkBlenders.h"
#import "SkBlurMaskFilter.h"
#import "SkColorMatrix.h"
#import "SkColorMatrixFilter.h"
#import "SkCornerPathEffect.h"
#import "SkDashPathEffect.h"
#import "SkDiscretePathEffect.h"
#import "SkGradientShader.h"
#import "SkHighContrastFilter.h"
#import "SkImageFilters.h"
#import "SkLumaColorFilter.h"
#import "SkOverdrawColorFilter.h"
#import "SkPerlinNoiseShader.h"
#import "SkRuntimeEffect.h"
#import "SkShaderMaskFilter.h"
#import "SkTableMaskFilter.h"
#import "SkTrimPathEffect.h"
#import "SkEncoder.h"
#import "SkICC.h"
#import "SkJpegEncoder.h"
#import "SkPngEncoder.h"
#import "SkWebpEncoder.h"
#import "GrD3DBackendContext.h"
#import "GrD3DTypes.h"
#import "GrGLMakeEGLInterface.h"
#import "GrGLMakeEpoxyEGLInterface.h"
#import "GrGLMakeGLXInterface.h"
#import "GrGLAssembleHelpers.h"
#import "GrGLAssembleInterface.h"
#import "GrGLBackendSurface.h"
#import "GrGLConfig.h"
#import "GrGLDirectContext.h"
#import "GrGLExtensions.h"
#import "GrGLFunctions.h"
#import "GrGLInterface.h"
#import "GrGLMakeWebGLInterface.h"
#import "GrGLTypes.h"
#import "GrGLMakeIOSInterface.h"
#import "GrGLMakeMacInterface.h"
#import "GrGLMakeWinInterface.h"
#import "GrBackendSemaphore.h"
#import "GrBackendSurface.h"
#import "GrContextOptions.h"
#import "GrContextThreadSafeProxy.h"
#import "GrDirectContext.h"
#import "GrDriverBugWorkarounds.h"
#import "GrDriverBugWorkaroundsAutogen.h"
#import "GrExternalTextureGenerator.h"
#import "GrRecordingContext.h"
#import "GrTypes.h"
#import "GrYUVABackendTextures.h"
#import "GrMockTypes.h"
#import "GrMtlBackendContext.h"
#import "GrMtlBackendSemaphore.h"
#import "GrMtlBackendSurface.h"
#import "GrMtlDirectContext.h"
#import "GrMtlTypes.h"
#import "SkSurfaceMetal.h"
#import "SkImageGanesh.h"
#import "SkMeshGanesh.h"
#import "SkSurfaceGanesh.h"
#import "GrBackendDrawableInfo.h"
#import "GrVkBackendSemaphore.h"
#import "GrVkBackendSurface.h"
#import "GrVkDirectContext.h"
#import "GrVkTypes.h"
#import "GpuTypes.h"
#import "BackendSemaphore.h"
#import "BackendTexture.h"
#import "Context.h"
#import "ContextOptions.h"
#import "DawnBackendContext.h"
#import "DawnGraphiteTypes.h"
#import "DawnTypes.h"
#import "DawnUtils.h"
#import "GraphiteTypes.h"
#import "Image.h"
#import "ImageProvider.h"
#import "LogPriority.h"
#import "MtlBackendContext.h"
#import "MtlGraphiteTypes.h"
#import "MtlGraphiteTypesUtils.h"
#import "MtlGraphiteTypes_cpp.h"
#import "MtlGraphiteUtils.h"
#import "PaintOptions.h"
#import "Precompile.h"
#import "PrecompileBase.h"
#import "PrecompileBlender.h"
#import "PrecompileColorFilter.h"
#import "PrecompileImageFilter.h"
#import "PrecompileMaskFilter.h"
#import "PrecompileRuntimeEffect.h"
#import "PrecompileShader.h"
#import "PrecompileContext.h"
#import "Recorder.h"
#import "Recording.h"
#import "Surface.h"
#import "TextureInfo.h"
#import "VulkanGraphiteContext.h"
#import "VulkanGraphiteTypes.h"
#import "VulkanGraphiteUtils.h"
#import "YUVABackendTextures.h"
#import "MtlMemoryAllocator.h"
#import "MutableTextureState.h"
#import "ShaderErrorHandler.h"
#import "VulkanBackendContext.h"
#import "VulkanExtensions.h"
#import "VulkanMemoryAllocator.h"
#import "VulkanMutableTextureState.h"
#import "VulkanTypes.h"
#import "SkPathOps.h"
#import "SkCFObject.h"
#import "SkFontConfigInterface.h"
#import "SkFontMgr_android.h"
#import "SkFontMgr_android_ndk.h"
#import "SkFontMgr_data.h"
#import "SkFontMgr_directory.h"
#import "SkFontMgr_empty.h"
#import "SkFontMgr_Fontations.h"
#import "SkFontMgr_fontconfig.h"
#import "SkFontMgr_FontConfigInterface.h"
#import "SkFontMgr_fuchsia.h"
#import "SkFontMgr_mac_ct.h"
#import "SkFontScanner_Fontations.h"
#import "SkFontScanner_FreeType.h"
#import "SkImageGeneratorCG.h"
#import "SkImageGeneratorNDK.h"
#import "SkImageGeneratorWIC.h"
#import "SkTypeface_fontations.h"
#import "SkTypeface_mac.h"
#import "SkTypeface_win.h"
#import "SingleOwner.h"
#import "SkAlign.h"
#import "SkAlignedStorage.h"
#import "SkAnySubclass.h"
#import "SkAPI.h"
#import "SkASAN.h"
#import "SkAssert.h"
#import "SkAttributes.h"
#import "SkContainers.h"
#import "SkCPUTypes.h"
#import "SkDebug.h"
#import "SkDeque.h"
#import "SkFeatures.h"
#import "SkFixed.h"
#import "SkFloatingPoint.h"
#import "SkLoadUserConfig.h"
#import "SkMacros.h"
#import "SkMalloc.h"
#import "SkMath.h"
#import "SkMutex.h"
#import "SkNoncopyable.h"
#import "SkOnce.h"
#import "SkPoint_impl.h"
#import "SkSafe32.h"
#import "SkSemaphore.h"
#import "SkSpan_impl.h"
#import "SkTArray.h"
#import "SkTDArray.h"
#import "SkTemplates.h"
#import "SkTFitsIn.h"
#import "SkThreadAnnotations.h"
#import "SkThreadID.h"
#import "SkTLogic.h"
#import "SkTo.h"
#import "SkTPin.h"
#import "SkTypeTraits.h"
#import "GrDeferredDisplayList.h"
#import "GrDeferredDisplayListRecorder.h"
#import "GrPromiseImageTexture.h"
#import "GrSurfaceCharacterization.h"
#import "GrVkSecondaryCBDrawContext.h"
#import "SkChromeRemoteGlyphCache.h"
#import "SkDiscardableMemory.h"
#import "SkImageChromium.h"
#import "SkPMColor.h"
#import "Slug.h"
#import "GrContext_Base.h"
#import "GrD3DTypesMinimal.h"
#import "GrImageContext.h"
#import "GrTextureGenerator.h"
#import "GrTypesPriv.h"
#import "SkiaVulkan.h"
#import "SkEncodedInfo.h"
#import "SkExif.h"
#import "SkGainmapInfo.h"
#import "SkGainmapShader.h"
#import "SkIDChangeListener.h"
#import "SkJpegGainmapEncoder.h"
#import "SkJpegMetadataDecoder.h"
#import "SkPathRef.h"
#import "SkSLSampleUsage.h"
#import "SkWeakRefCnt.h"
#import "SkXmp.h"
#import "SkSLDebugTrace.h"
#import "SkSLVersion.h"
#import "SkSVGCanvas.h"
#import "vk_platform.h"
#import "vulkan_video_codecs_common.h"
#import "vulkan_video_codec_h264std.h"
#import "vulkan_video_codec_h264std_decode.h"
#import "vulkan_video_codec_h265std.h"
#import "vulkan_video_codec_h265std_decode.h"
#import "vulkan.h"
#import "vulkan_android.h"
#import "vulkan_core.h"
#import "vulkan_ios.h"
#import "vulkan_macos.h"
#import "vulkan_win32.h"
#import "vulkan_xcb.h"
#import "SkCGUtils.h"
#import "SkCamera.h"
#import "SkCanvasStateUtils.h"
#import "SkCustomTypeface.h"
#import "SkEventTracer.h"
#import "SkNoDrawCanvas.h"
#import "SkNullCanvas.h"
#import "SkNWayCanvas.h"
#import "SkOrderedFontMgr.h"
#import "SkPaintFilterCanvas.h"
#import "SkParse.h"
#import "SkParsePath.h"
#import "SkShadowUtils.h"
#import "SkTextUtils.h"
#import "SkTraceEventPhase.h"
#import "skcms.h"
#import "skcms_internals.h"
#import "skcms_public.h"
#import "skcms_Transform.h"
#import "Transform_inl.h"
#import "DartTypes.h"
#import "FontArguments.h"
#import "FontCollection.h"
#import "Metrics.h"
#import "Paragraph.h"
#import "ParagraphBuilder.h"
#import "ParagraphCache.h"
#import "ParagraphPainter.h"
#import "ParagraphStyle.h"
#import "TextShadow.h"
#import "TextStyle.h"
#import "TypefaceFontProvider.h"
#import "SkResources.h"
#import "SkShaper.h"
#import "SkShaper_coretext.h"
#import "SkShaper_factory.h"
#import "SkShaper_harfbuzz.h"
#import "SkShaper_skunicode.h"
#import "SkUnicode.h"
#import "SkSVGAttribute.h"
#import "SkSVGAttributeParser.h"
#import "SkSVGCircle.h"
#import "SkSVGClipPath.h"
#import "SkSVGContainer.h"
#import "SkSVGDefs.h"
#import "SkSVGDOM.h"
#import "SkSVGEllipse.h"
#import "SkSVGFe.h"
#import "SkSVGFeBlend.h"
#import "SkSVGFeColorMatrix.h"
#import "SkSVGFeComponentTransfer.h"
#import "SkSVGFeComposite.h"
#import "SkSVGFeDisplacementMap.h"
#import "SkSVGFeFlood.h"
#import "SkSVGFeGaussianBlur.h"
#import "SkSVGFeImage.h"
#import "SkSVGFeLighting.h"
#import "SkSVGFeLightSource.h"
#import "SkSVGFeMerge.h"
#import "SkSVGFeMorphology.h"
#import "SkSVGFeOffset.h"
#import "SkSVGFeTurbulence.h"
#import "SkSVGFilter.h"
#import "SkSVGFilterContext.h"
#import "SkSVGG.h"
#import "SkSVGGradient.h"
#import "SkSVGHiddenContainer.h"
#import "SkSVGIDMapper.h"
#import "SkSVGImage.h"
#import "SkSVGLine.h"
#import "SkSVGLinearGradient.h"
#import "SkSVGMask.h"
#import "SkSVGNode.h"
#import "SkSVGOpenTypeSVGDecoder.h"
#import "SkSVGPath.h"
#import "SkSVGPattern.h"
#import "SkSVGPoly.h"
#import "SkSVGRadialGradient.h"
#import "SkSVGRect.h"
#import "SkSVGRenderContext.h"
#import "SkSVGShape.h"
#import "SkSVGStop.h"
#import "SkSVGSVG.h"
#import "SkSVGText.h"
#import "SkSVGTransformableNode.h"
#import "SkSVGTypes.h"
#import "SkSVGUse.h"
#import "SkSVGValue.h"
#import "SkMathPriv.h"
#import "SkTInternalLList.h"
#import "SkTLazy.h"
#import "SkUTF.h"
#import "SkChecksum.h"
#import "SkLRUCache.h"
#import "SkTHash.h"
#import "GrGLDefines.h"
#import "RNSkLog.h"
#import "RNSkMeasureTime.h"
#import "RNSkTimingInfo.h"
#import "RNSkTypedArray.h"

FOUNDATION_EXPORT double react_native_skiaVersionNumber;
FOUNDATION_EXPORT const unsigned char react_native_skiaVersionString[];


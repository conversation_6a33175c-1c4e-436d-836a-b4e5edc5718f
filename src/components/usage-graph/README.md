# Usage Graph Component Library

A comprehensive React Native component library for displaying energy usage data with interactive filtering capabilities. Built specifically for the Digital Core API response format.

## Features

- **Stacked Bar Charts**: Multiple data series in each bar
- **Interactive Touch Handling**: Tap bars to show detailed information
- **Responsive Design**: Adapts to different screen sizes
- **Filtering**: Product type, time interval, and display mode filtering
- **Previous Year Comparison**: Optional line overlay
- **Temperature Data**: Optional temperature correlation line
- **Loading States**: Animated skeleton screens
- **Error Handling**: Graceful error boundaries
- **Accessibility**: Screen reader support and proper contrast

## Quick Start

```tsx
import React from 'react';
import { UsageGraph, UsageGraphErrorBoundary } from '@/src/components/usage-graph';
import { useDigitalCore } from '@/src/api/hooks/useDigitalCore';

export const EnergyDashboard = () => {
  const { getUsages, customerId } = useDigitalCore();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getUsages(accountId, {
          aggregation: 'Day',
          interval: 'Day',
          start: '2024-01-01',
          end: '2024-01-31',
          addBudget: true,
          addWeather: true,
        });
        
        setData(response?.data?.usages?.[0]?.entries || []);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (customerId) {
      fetchData();
    }
  }, [customerId]);

  return (
    <UsageGraphErrorBoundary>
      <UsageGraph
        data={data}
        loading={loading}
        error={error}
        height={350}
        onBarPress={(event) => {
          console.log('Bar pressed:', event);
        }}
        onFilterChange={(filters) => {
          console.log('Filters changed:', filters);
        }}
      />
    </UsageGraphErrorBoundary>
  );
};
```

## Components

### UsageGraph (Main Component)

The primary component that combines all functionality.

```tsx
interface UsageGraphProps {
  data: Usages_V3_Aggregation_Interval_UsagesEntry[];
  config?: Partial<ChartConfig>;
  onBarPress?: (event: TouchEventData) => void;
  onFilterChange?: (filters: FilterConfig) => void;
  loading?: boolean;
  error?: string | null;
  height?: number;
}
```

### UsageFilters

Provides filtering controls for the graph.

```tsx
<UsageFilters
  filters={filters}
  onFiltersChange={setFilters}
  availableProducts={['electricity', 'gas', 'warmth']}
  disabled={loading}
/>
```

### UsageChart

The main chart component using SVG for cross-platform compatibility.

```tsx
<UsageChart
  data={transformedData}
  selectedProducts={['electricity', 'gas']}
  displayMode="consumption"
  showPreviousYear={true}
  showTemperature={false}
  onBarPress={handleBarPress}
  height={300}
/>
```

### UsageLegend

Color-coded legend with interactive toggles.

```tsx
<UsageLegend
  items={legendItems}
  onItemPress={handleLegendToggle}
  horizontal={true}
  showValues={false}
/>
```

### UsageTooltip

Modal tooltip showing detailed information.

```tsx
<UsageTooltip
  tooltip={tooltipData}
  onClose={handleTooltipClose}
  displayMode="consumption"
/>
```

### LoadingSkeleton

Animated loading skeleton.

```tsx
<LoadingSkeleton
  height={300}
  showFilters={true}
/>
```

## Data Transformation

The library automatically transforms Digital Core API responses:

```tsx
import { transformUsageEntries } from '@/src/components/usage-graph';

// Transform raw API data
const chartData = transformUsageEntries(apiResponse.entries, 'consumption');

// Filter by selected products
const filteredData = filterByProducts(chartData, ['electricity', 'gas']);

// Get available products from data
const availableProducts = getAvailableProducts(chartData);
```

## Customization

### Chart Configuration

```tsx
const customConfig = {
  height: 400,
  showLegend: true,
  showGrid: true,
  showTooltips: true,
  animationDuration: 500,
  colors: {
    electricity: '#2196F3',
    gas: '#FF9800',
    warmth: '#F44336',
    // ... other colors
  },
};

<UsageGraph data={data} config={customConfig} />
```

### Custom Colors

```tsx
import { defaultProductColors } from '@/src/components/usage-graph';

const customColors = {
  ...defaultProductColors,
  electricity: '#00BCD4',
  gas: '#4CAF50',
};
```

## Event Handling

### Bar Press Events

```tsx
const handleBarPress = (event: TouchEventData) => {
  const { entry, product, value, x, y } = event;
  
  // Show custom modal or navigate to detail view
  navigation.navigate('UsageDetail', {
    date: entry.date,
    product,
    value,
  });
};
```

### Filter Changes

```tsx
const handleFilterChange = (filters: FilterConfig) => {
  // Update URL params or save to storage
  updateUrlParams(filters);
  
  // Fetch new data if needed
  if (filters.timeInterval !== previousInterval) {
    fetchNewData(filters.timeInterval);
  }
};
```

## Accessibility

The components include comprehensive accessibility support:

- Screen reader labels and hints
- Proper focus management
- High contrast support
- Touch target sizing (minimum 44pt)
- Semantic roles and states

## Performance

- Efficient SVG rendering with minimal re-renders
- Virtualized scrolling for large datasets
- Memoized calculations and transformations
- Optimized animations using native driver

## Error Handling

```tsx
// Use error boundary for graceful error handling
<UsageGraphErrorBoundary
  fallback={({ error }) => (
    <CustomErrorComponent error={error} />
  )}
>
  <UsageGraph data={data} />
</UsageGraphErrorBoundary>
```

## TypeScript Support

Full TypeScript support with comprehensive type definitions:

```tsx
import type {
  ProductType,
  TimeInterval,
  DisplayMode,
  UsageEntry,
  FilterConfig,
  TouchEventData,
} from '@/src/components/usage-graph';
```

## Dependencies

- React Native SVG (for chart rendering)
- React Native (core components)
- Your design system components

## Browser/Platform Support

- iOS (React Native)
- Android (React Native)
- Web (React Native Web with SVG support)

## Contributing

When adding new features:

1. Update type definitions in `types.ts`
2. Add data transformation utilities in `utils/dataTransformers.ts`
3. Create comprehensive tests
4. Update documentation
5. Ensure accessibility compliance

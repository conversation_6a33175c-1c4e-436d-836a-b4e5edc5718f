/**
 * Complete Example: Energy Usage Dashboard
 * Demonstrates how to use the Usage Graph component library
 */

import { useDigitalCore } from '@/src/api/hooks/useDigitalCore';
import { useAppTheme } from '@/src/design-system';
import { Text } from '@/src/design-system/components/Text';
import React, { useCallback, useEffect, useState } from 'react';
import { Alert, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import {
    FilterConfig,
    TouchEventData,
    UsageGraph,
    UsageGraphErrorBoundary,
    defaultUsageGraphConfig
} from '../index';

interface EnergyUsageExampleProps {
  accountId?: number;
  initialPeriod?: 'week' | 'month' | 'year';
}

export const EnergyUsageExample: React.FC<EnergyUsageExampleProps> = ({
  accountId = 1,
  initialPeriod = 'month',
}) => {
  const { theme } = useAppTheme();
  const { getUsages, customerId, isAuthenticated, error: authError } = useDigitalCore();

  // State management
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPeriod, setCurrentPeriod] = useState(initialPeriod);
  const [selectedEntry, setSelectedEntry] = useState(null);

  // Fetch usage data
  const fetchUsageData = useCallback(async (period: string) => {
    if (!isAuthenticated || !customerId) {
      setError('User not authenticated');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Calculate date range based on period
      const now = new Date();
      const endDate = now.toISOString().split('T')[0];
      let startDate: string;
      let aggregation: string;
      let interval: string;

      switch (period) {
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          startDate = weekAgo.toISOString().split('T')[0];
          aggregation = 'Day';
          interval = 'Day';
          break;
        case 'month':
          const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
          startDate = monthAgo.toISOString().split('T')[0];
          aggregation = 'Day';
          interval = 'Day';
          break;
        case 'year':
          const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
          startDate = yearAgo.toISOString().split('T')[0];
          aggregation = 'Month';
          interval = 'Month';
          break;
        default:
          throw new Error('Invalid period');
      }

      console.log('Fetching usage data:', {
        customerId,
        accountId,
        period,
        startDate,
        endDate,
        aggregation,
        interval,
      });

      const response = await getUsages(accountId, {
        aggregation: aggregation as any,
        interval: interval as any,
        start: startDate,
        end: endDate,
        addBudget: true,
        addWeather: true,
        extrapolate: false,
      });

      if (!response?.data?.usages?.[0]?.entries) {
        throw new Error('No usage data available for the selected period');
      }

      setData(response.data.usages[0].entries);
      console.log('Successfully loaded usage data:', response.data.usages[0].entries.length, 'entries');

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch usage data';
      setError(errorMessage);
      console.error('Error fetching usage data:', err);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, customerId, accountId, getUsages]);

  // Initial data fetch
  useEffect(() => {
    fetchUsageData(currentPeriod);
  }, [fetchUsageData, currentPeriod]);

  // Handle period change
  const handlePeriodChange = useCallback((newPeriod: string) => {
    setCurrentPeriod(newPeriod);
  }, []);

  // Handle bar press
  const handleBarPress = useCallback((event: TouchEventData) => {
    console.log('Bar pressed:', event);
    setSelectedEntry(event.entry);
    
    // Show detailed information
    Alert.alert(
      'Usage Details',
      `Date: ${new Date(event.entry.date).toLocaleDateString()}\n` +
      `Product: ${event.product}\n` +
      `Value: ${event.value.toFixed(2)}`,
      [{ text: 'OK' }]
    );
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((filters: FilterConfig) => {
    console.log('Filters changed:', filters);
    
    // You could save filters to storage or update URL params here
    // AsyncStorage.setItem('usageFilters', JSON.stringify(filters));
  }, []);

  // Handle retry
  const handleRetry = useCallback(() => {
    fetchUsageData(currentPeriod);
  }, [fetchUsageData, currentPeriod]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: 16,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
    },
    periodSelector: {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 12,
      paddingVertical: 16,
      paddingHorizontal: 16,
      backgroundColor: theme.colors.surface,
    },
    periodButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    periodButtonActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    periodButtonText: {
      color: theme.colors.onSurface,
      fontWeight: '500',
    },
    periodButtonTextActive: {
      color: theme.colors.onPrimary,
    },
    content: {
      flex: 1,
    },
    errorContainer: {
      padding: 20,
      margin: 16,
      backgroundColor: theme.colors.errorContainer,
      borderRadius: 8,
      alignItems: 'center',
    },
    errorText: {
      color: theme.colors.onErrorContainer,
      textAlign: 'center',
      marginBottom: 16,
    },
    retryButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: theme.colors.error,
      borderRadius: 6,
    },
    retryButtonText: {
      color: theme.colors.onError,
      fontWeight: '500',
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      padding: 16,
      backgroundColor: theme.colors.surface,
      marginHorizontal: 16,
      marginTop: 16,
      borderRadius: 8,
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      marginTop: 4,
    },
  });

  // Calculate summary statistics
  const summaryStats = React.useMemo(() => {
    if (!data || data.length === 0) return null;

    const totalElectricity = data.reduce((sum, entry) => 
      sum + (entry.actual?.electricity?.high || 0) + (entry.actual?.electricity?.low || 0), 0
    );
    
    const totalGas = data.reduce((sum, entry) => 
      sum + (entry.actual?.gas?.high || 0) + (entry.actual?.gas?.low || 0), 0
    );
    
    const totalCost = data.reduce((sum, entry) => 
      sum + (entry.actual?.totalCostInclVat?.value || 0), 0
    );

    return {
      electricity: totalElectricity.toFixed(1),
      gas: totalGas.toFixed(1),
      cost: totalCost.toFixed(2),
    };
  }, [data]);

  // Custom error fallback
  const ErrorFallback = ({ error }: { error: Error }) => (
    <View style={styles.errorContainer}>
      <Text style={styles.errorText}>
        Chart Error: {error.message}
      </Text>
      <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
        <Text style={styles.retryButtonText}>Retry</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Energy Usage Dashboard</Text>
        <Text style={styles.subtitle}>
          {isAuthenticated ? `Customer: ${customerId}` : 'Not authenticated'}
        </Text>
      </View>

      {/* Period Selector */}
      <View style={styles.periodSelector}>
        {['week', 'month', 'year'].map(period => (
          <TouchableOpacity
            key={period}
            style={[
              styles.periodButton,
              currentPeriod === period && styles.periodButtonActive,
            ]}
            onPress={() => handlePeriodChange(period)}
          >
            <Text style={[
              styles.periodButtonText,
              currentPeriod === period && styles.periodButtonTextActive,
            ]}>
              {period.charAt(0).toUpperCase() + period.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content}>
        {/* Summary Statistics */}
        {summaryStats && (
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{summaryStats.electricity}</Text>
              <Text style={styles.statLabel}>kWh Electricity</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{summaryStats.gas}</Text>
              <Text style={styles.statLabel}>m³ Gas</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>€{summaryStats.cost}</Text>
              <Text style={styles.statLabel}>Total Cost</Text>
            </View>
          </View>
        )}

        {/* Usage Graph */}
        <UsageGraphErrorBoundary fallback={ErrorFallback}>
          <UsageGraph
            data={data}
            loading={loading}
            error={error || authError?.message}
            height={350}
            config={{
              ...defaultUsageGraphConfig,
              showLegend: true,
              showGrid: true,
              animationDuration: 300,
            }}
            onBarPress={handleBarPress}
            onFilterChange={handleFilterChange}
          />
        </UsageGraphErrorBoundary>
      </ScrollView>
    </View>
  );
};



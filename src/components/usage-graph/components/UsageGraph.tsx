/**
 * Main Usage Graph Component
 * Combines all sub-components into a complete usage visualization
 */

import { useAppTheme } from '@/src/design-system';
import { Text } from '@/src/design-system/components/Text';
import React, { useCallback, useMemo, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import {
    FilterConfig,
    ProductType,
    TooltipData,
    TouchEventData,
    UsageGraphProps
} from '../types';
import {
    filterByProducts,
    getAvailableProducts,
    transformUsageEntries
} from '../utils/dataTransformers';
import { LoadingSkeleton } from './LoadingSkeleton';
import { UsageChart } from './UsageChart';
import { UsageFilters } from './UsageFilters';
import { UsageLegend, createLegendItems, defaultProductColors } from './UsageLegend';
import { UsageTooltip } from './UsageTooltip';

export const UsageGraph: React.FC<UsageGraphProps> = ({
  data,
  config = {},
  onBarPress,
  onFilterChange,
  loading = false,
  error = null,
  height = 300,
}) => {
  const { theme } = useAppTheme();

  // Transform raw data to chart format
  const transformedData = useMemo(() => {
    console.log('UsageGraph received data:', data);
    if (!data || data.length === 0) {
      console.log('No data available for transformation');
      return [];
    }
    console.log('Transforming', data.length, 'entries');
    const result = transformUsageEntries(data, 'consumption');
    console.log('Transformed data:', result);
    return result;
  }, [data]);

  // Get available products from data
  const availableProducts = useMemo(() => 
    getAvailableProducts(transformedData), [transformedData]
  );

  // Filter state
  const [filters, setFilters] = useState<FilterConfig>({
    products: availableProducts.slice(0, 3), // Show first 3 products by default
    timeInterval: 'Day',
    displayMode: 'consumption',
    showPreviousYear: false,
    showTemperature: false,
    showProjected: true,
  });

  // Tooltip state
  const [tooltip, setTooltip] = useState<TooltipData>({
    visible: false,
    x: 0,
    y: 0,
    entry: null,
    product: null,
  });

  // Update filters when available products change
  React.useEffect(() => {
    if (availableProducts.length > 0 && filters.products.length === 0) {
      setFilters(prev => ({
        ...prev,
        products: availableProducts.slice(0, 3),
      }));
    }
  }, [availableProducts, filters.products.length]);

  // Filter data based on current filters
  const filteredData = useMemo(() => {
    if (filters.products.length === 0) return [];
    return filterByProducts(transformedData, filters.products);
  }, [transformedData, filters.products]);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: FilterConfig) => {
    setFilters(newFilters);
    onFilterChange?.(newFilters);
  }, [onFilterChange]);

  // Handle bar press
  const handleBarPress = useCallback((event: TouchEventData) => {
    setTooltip({
      visible: true,
      x: event.x,
      y: event.y,
      entry: event.entry,
      product: event.product,
    });
    onBarPress?.(event);
  }, [onBarPress]);

  // Handle tooltip close
  const handleTooltipClose = useCallback(() => {
    setTooltip(prev => ({ ...prev, visible: false }));
  }, []);

  // Handle legend item press (toggle product visibility)
  const handleLegendItemPress = useCallback((product: ProductType) => {
    setFilters(prev => ({
      ...prev,
      products: prev.products.includes(product)
        ? prev.products.filter(p => p !== product)
        : [...prev.products, product],
    }));
  }, []);

  // Create legend items
  const legendItems = useMemo(() => 
    createLegendItems(
      availableProducts,
      filters.products,
      defaultProductColors
    ), [availableProducts, filters.products]
  );

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
    },
    errorContainer: {
      padding: 20,
      backgroundColor: theme.colors.errorContainer,
      borderRadius: 8,
      margin: 16,
    },
    errorText: {
      color: theme.colors.onErrorContainer,
      textAlign: 'center',
      fontSize: 16,
    },
    emptyContainer: {
      padding: 40,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      margin: 16,
    },
    emptyText: {
      color: theme.colors.onSurfaceVariant,
      fontSize: 16,
      textAlign: 'center',
    },
    chartSection: {
      marginBottom: 16,
    },
    legendSection: {
      paddingHorizontal: 16,
      paddingBottom: 16,
    },
  });

  // Loading state
  if (loading) {
    return <LoadingSkeleton height={height} showFilters={true} />;
  }

  // Error state
  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {error}
          </Text>
        </View>
      </View>
    );
  }

  // Empty state
  if (!data || data.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            No usage data available for the selected period.
          </Text>
        </View>
      </View>
    );
  }

  // No products selected
  if (filters.products.length === 0) {
    return (
      <View style={styles.container}>
        <UsageFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          availableProducts={availableProducts}
        />
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            Please select at least one energy type to display.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Filters */}
      <UsageFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        availableProducts={availableProducts}
      />

      {/* Chart */}
      <View style={styles.chartSection}>
        <UsageChart
          data={filteredData}
          selectedProducts={filters.products}
          displayMode={filters.displayMode}
          showPreviousYear={filters.showPreviousYear}
          showTemperature={filters.showTemperature}
          config={config}
          onBarPress={handleBarPress}
          height={height}
        />
      </View>

      {/* Legend */}
      <View style={styles.legendSection}>
        <UsageLegend
          items={legendItems}
          onItemPress={handleLegendItemPress}
          horizontal={true}
          showValues={false}
        />
      </View>

      {/* Tooltip */}
      <UsageTooltip
        tooltip={tooltip}
        onClose={handleTooltipClose}
        displayMode={filters.displayMode}
      />
    </View>
  );
};

// Export default configuration
export const defaultUsageGraphConfig = {
  height: 300,
  showLegend: true,
  showGrid: true,
  showTooltips: true,
  animationDuration: 300,
};

// Export helper function to create initial filters
export const createInitialFilters = (
  availableProducts: ProductType[],
  maxProducts: number = 3
): FilterConfig => ({
  products: availableProducts.slice(0, maxProducts),
  timeInterval: 'Day',
  displayMode: 'consumption',
  showPreviousYear: false,
  showTemperature: false,
  showProjected: true,
});

// Export error boundary component
export class UsageGraphErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('UsageGraph Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      
      if (FallbackComponent && this.state.error) {
        return <FallbackComponent error={this.state.error} />;
      }

      return (
        <View style={{ padding: 20, backgroundColor: '#ffebee', borderRadius: 8 }}>
          <Text style={{ color: '#c62828', textAlign: 'center' }}>
            Something went wrong while rendering the usage graph.
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

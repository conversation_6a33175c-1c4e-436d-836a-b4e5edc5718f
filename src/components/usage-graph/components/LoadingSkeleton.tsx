/**
 * Loading Skeleton Component
 * Displays animated skeleton while data is loading
 */

import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { useAppTheme } from '@/src/design-system';

interface LoadingSkeletonProps {
  height?: number;
  showFilters?: boolean;
}

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  height = 300,
  showFilters = true,
}) => {
  const { theme } = useAppTheme();
  const { width: screenWidth } = Dimensions.get('window');
  
  // Animation values
  const shimmerAnimation = useRef(new Animated.Value(0)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Shimmer effect
    const shimmer = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnimation, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnimation, {
          toValue: 0,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    );

    // Pulse effect
    const pulse = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 0.7,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );

    shimmer.start();
    pulse.start();

    return () => {
      shimmer.stop();
      pulse.stop();
    };
  }, [shimmerAnimation, pulseAnimation]);

  const shimmerTranslateX = shimmerAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [-screenWidth, screenWidth],
  });

  const styles = StyleSheet.create({
    container: {
      padding: 16,
    },
    filtersContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
    },
    filterSection: {
      marginBottom: 16,
    },
    filterTitle: {
      height: 16,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 4,
      marginBottom: 8,
      width: '30%',
    },
    filterChips: {
      flexDirection: 'row',
      gap: 8,
      marginBottom: 12,
    },
    filterChip: {
      height: 32,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 16,
      width: 80,
    },
    filterRow: {
      flexDirection: 'row',
      gap: 12,
    },
    filterPicker: {
      flex: 1,
      height: 40,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 8,
    },
    chartContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 16,
      height: height + 32, // Account for padding
    },
    chartHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    chartTitle: {
      height: 20,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 4,
      width: '40%',
    },
    chartControls: {
      flexDirection: 'row',
      gap: 8,
    },
    chartControl: {
      height: 32,
      width: 60,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 16,
    },
    chartArea: {
      height: height - 60,
      position: 'relative',
      overflow: 'hidden',
    },
    yAxis: {
      position: 'absolute',
      left: 0,
      top: 0,
      bottom: 40,
      width: 40,
      justifyContent: 'space-between',
      paddingVertical: 10,
    },
    yAxisLabel: {
      height: 12,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 2,
      width: 30,
    },
    xAxis: {
      position: 'absolute',
      bottom: 0,
      left: 40,
      right: 0,
      height: 40,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
      paddingHorizontal: 10,
    },
    xAxisLabel: {
      height: 12,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 2,
      width: 40,
    },
    barsContainer: {
      position: 'absolute',
      left: 50,
      right: 10,
      top: 10,
      bottom: 50,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
    },
    bar: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 2,
      width: 20,
    },
    legend: {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 16,
      marginTop: 16,
    },
    legendItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 6,
    },
    legendColor: {
      width: 12,
      height: 12,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 6,
    },
    legendLabel: {
      height: 14,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 2,
      width: 60,
    },
    shimmerOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
    },
  });

  const renderBars = () => {
    const barCount = 8;
    const bars = [];
    
    for (let i = 0; i < barCount; i++) {
      const height = Math.random() * 0.7 + 0.3; // Random height between 30% and 100%
      bars.push(
        <Animated.View
          key={i}
          style={[
            styles.bar,
            {
              height: `${height * 100}%`,
              opacity: pulseAnimation,
            },
          ]}
        />
      );
    }
    
    return bars;
  };

  const renderYAxisLabels = () => {
    const labelCount = 5;
    const labels = [];
    
    for (let i = 0; i < labelCount; i++) {
      labels.push(
        <Animated.View
          key={i}
          style={[styles.yAxisLabel, { opacity: pulseAnimation }]}
        />
      );
    }
    
    return labels;
  };

  const renderXAxisLabels = () => {
    const labelCount = 8;
    const labels = [];
    
    for (let i = 0; i < labelCount; i++) {
      labels.push(
        <Animated.View
          key={i}
          style={[styles.xAxisLabel, { opacity: pulseAnimation }]}
        />
      );
    }
    
    return labels;
  };

  return (
    <View style={styles.container}>
      {/* Filters Skeleton */}
      {showFilters && (
        <View style={styles.filtersContainer}>
          <View style={styles.filterSection}>
            <Animated.View style={[styles.filterTitle, { opacity: pulseAnimation }]} />
            <View style={styles.filterChips}>
              {[1, 2, 3, 4].map(i => (
                <Animated.View
                  key={i}
                  style={[styles.filterChip, { opacity: pulseAnimation }]}
                />
              ))}
            </View>
          </View>
          
          <View style={styles.filterRow}>
            <Animated.View style={[styles.filterPicker, { opacity: pulseAnimation }]} />
            <Animated.View style={[styles.filterPicker, { opacity: pulseAnimation }]} />
          </View>
        </View>
      )}

      {/* Chart Skeleton */}
      <View style={styles.chartContainer}>
        <View style={styles.chartHeader}>
          <Animated.View style={[styles.chartTitle, { opacity: pulseAnimation }]} />
          <View style={styles.chartControls}>
            <Animated.View style={[styles.chartControl, { opacity: pulseAnimation }]} />
            <Animated.View style={[styles.chartControl, { opacity: pulseAnimation }]} />
          </View>
        </View>

        <View style={styles.chartArea}>
          {/* Y-axis labels */}
          <View style={styles.yAxis}>
            {renderYAxisLabels()}
          </View>

          {/* X-axis labels */}
          <View style={styles.xAxis}>
            {renderXAxisLabels()}
          </View>

          {/* Bars */}
          <View style={styles.barsContainer}>
            {renderBars()}
          </View>

          {/* Shimmer overlay */}
          <Animated.View
            style={[
              styles.shimmerOverlay,
              {
                transform: [{ translateX: shimmerTranslateX }],
              },
            ]}
          />
        </View>

        {/* Legend */}
        <View style={styles.legend}>
          {[1, 2, 3].map(i => (
            <View key={i} style={styles.legendItem}>
              <Animated.View style={[styles.legendColor, { opacity: pulseAnimation }]} />
              <Animated.View style={[styles.legendLabel, { opacity: pulseAnimation }]} />
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

/**
 * Usage Filters Component
 * Provides filtering controls for the usage graph
 */

import { useAppTheme } from '@/src/design-system';
import { Text } from '@/src/design-system/components/Text';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { DisplayMode, ProductType, TimeInterval, UsageFiltersProps } from '../types';

// Mock Picker component - replace with your preferred picker library
const Picker = ({ 
  selectedValue, 
  onValueChange, 
  children, 
  style,
  enabled = true 
}: {
  selectedValue: any;
  onValueChange: (value: any) => void;
  children: React.ReactNode;
  style?: any;
  enabled?: boolean;
}) => {
  // This would be replaced with actual picker implementation
  // For now, returning a placeholder
  return (
    <View style={[{ padding: 12, borderWidth: 1, borderRadius: 8 }, style]}>
      <Text>{selectedValue}</Text>
    </View>
  );
};

const PickerItem = ({ label, value }: { label: string; value: any }) => null;

export const UsageFilters: React.FC<UsageFiltersProps> = ({
  filters,
  onFiltersChange,
  availableProducts,
  disabled = false,
}) => {
  const { theme } = useAppTheme();

  const productLabels: Record<ProductType, string> = {
    electricity: 'Electricity',
    gas: 'Gas',
    warmth: 'Heating',
    tapWater: 'Water',
    cooling: 'Cooling',
    redelivery: 'Redelivery',
    produced: 'Solar Production',
  };

  const intervalLabels: Record<TimeInterval, string> = {
    Hour: 'Hourly',
    Day: 'Daily',
    Week: 'Weekly',
    Month: 'Monthly',
    Year: 'Yearly',
  };

  const displayModeLabels: Record<DisplayMode, string> = {
    consumption: 'Usage',
    cost: 'Cost',
  };

  const handleProductToggle = (product: ProductType) => {
    const newProducts = filters.products.includes(product)
      ? filters.products.filter(p => p !== product)
      : [...filters.products, product];
    
    onFiltersChange({
      ...filters,
      products: newProducts,
    });
  };

  const handleIntervalChange = (interval: TimeInterval) => {
    onFiltersChange({
      ...filters,
      timeInterval: interval,
    });
  };

  const handleDisplayModeChange = (mode: DisplayMode) => {
    onFiltersChange({
      ...filters,
      displayMode: mode,
    });
  };

  const handleToggleOption = (option: keyof FilterConfig) => {
    onFiltersChange({
      ...filters,
      [option]: !filters[option],
    });
  };

  const styles = StyleSheet.create({
    container: {
      padding: 16,
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      marginBottom: 16,
    },
    section: {
      marginBottom: 16,
    },
    sectionTitle: {
      marginBottom: 8,
      color: theme.colors.onSurface,
      fontWeight: '600',
    },
    productGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    productChip: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    productChipSelected: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    productChipText: {
      fontSize: 12,
      color: theme.colors.onSurface,
    },
    productChipTextSelected: {
      color: theme.colors.onPrimary,
    },
    pickerContainer: {
      borderWidth: 1,
      borderColor: theme.colors.outline,
      borderRadius: 8,
      backgroundColor: theme.colors.background,
    },
    picker: {
      color: theme.colors.onSurface,
    },
    toggleRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
    },
    toggleButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    toggleButtonActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    toggleText: {
      fontSize: 14,
      color: theme.colors.onSurface,
    },
    toggleTextActive: {
      color: theme.colors.onPrimary,
    },
    row: {
      flexDirection: 'row',
      gap: 12,
    },
    flex1: {
      flex: 1,
    },
  });

  return (
    <View style={styles.container}>
      {/* Product Selection */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Energy Types</Text>
        <View style={styles.productGrid}>
          {availableProducts.map(product => {
            const isSelected = filters.products.includes(product);
            return (
              <TouchableOpacity
                key={product}
                style={[
                  styles.productChip,
                  isSelected && styles.productChipSelected,
                ]}
                onPress={() => handleProductToggle(product)}
                disabled={disabled}
                accessibilityRole="checkbox"
                accessibilityState={{ checked: isSelected }}
                accessibilityLabel={`${productLabels[product]} ${isSelected ? 'selected' : 'not selected'}`}
              >
                <Text style={[
                  styles.productChipText,
                  isSelected && styles.productChipTextSelected,
                ]}>
                  {productLabels[product]}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>

      {/* Time Interval and Display Mode */}
      <View style={[styles.section, styles.row]}>
        <View style={styles.flex1}>
          <Text style={styles.sectionTitle}>Time Period</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={filters.timeInterval}
              onValueChange={handleIntervalChange}
              style={styles.picker}
              enabled={!disabled}
            >
              {Object.entries(intervalLabels).map(([value, label]) => (
                <PickerItem key={value} label={label} value={value as TimeInterval} />
              ))}
            </Picker>
          </View>
        </View>

        <View style={styles.flex1}>
          <Text style={styles.sectionTitle}>Display</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={filters.displayMode}
              onValueChange={handleDisplayModeChange}
              style={styles.picker}
              enabled={!disabled}
            >
              {Object.entries(displayModeLabels).map(([value, label]) => (
                <PickerItem key={value} label={label} value={value as DisplayMode} />
              ))}
            </Picker>
          </View>
        </View>
      </View>

      {/* Toggle Options */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Options</Text>
        
        <View style={styles.toggleRow}>
          <Text>Show Previous Year</Text>
          <TouchableOpacity
            style={[
              styles.toggleButton,
              filters.showPreviousYear && styles.toggleButtonActive,
            ]}
            onPress={() => handleToggleOption('showPreviousYear')}
            disabled={disabled}
            accessibilityRole="switch"
            accessibilityState={{ checked: filters.showPreviousYear }}
          >
            <Text style={[
              styles.toggleText,
              filters.showPreviousYear && styles.toggleTextActive,
            ]}>
              {filters.showPreviousYear ? 'ON' : 'OFF'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.toggleRow}>
          <Text>Show Temperature</Text>
          <TouchableOpacity
            style={[
              styles.toggleButton,
              filters.showTemperature && styles.toggleButtonActive,
            ]}
            onPress={() => handleToggleOption('showTemperature')}
            disabled={disabled}
            accessibilityRole="switch"
            accessibilityState={{ checked: filters.showTemperature }}
          >
            <Text style={[
              styles.toggleText,
              filters.showTemperature && styles.toggleTextActive,
            ]}>
              {filters.showTemperature ? 'ON' : 'OFF'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};



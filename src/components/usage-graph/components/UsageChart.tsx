/**
 * Usage Chart Component
 * Main chart component using React Native SVG for cross-platform compatibility
 */

import { useAppTheme } from '@/src/design-system';
import { Text } from '@/src/design-system/components/Text';
import React, { useMemo } from 'react';
import { Dimensions, ScrollView, StyleSheet, View } from 'react-native';
import Svg, { G, Line, Rect, Text as SvgText } from 'react-native-svg';
import { ChartConfig, DisplayMode, ProductType, TouchEventData, UsageEntry } from '../types';
import { calculateDomain, formatDateForInterval } from '../utils/dataTransformers';

interface UsageChartProps {
  data: UsageEntry[];
  selectedProducts: ProductType[];
  displayMode: DisplayMode;
  showPreviousYear?: boolean;
  showTemperature?: boolean;
  config?: Partial<ChartConfig>;
  onBarPress?: (event: TouchEventData) => void;
  height?: number;
}

export const UsageChart: React.FC<UsageChartProps> = ({
  data,
  selectedProducts,
  displayMode,
  showPreviousYear = false,
  showTemperature = false,
  config = {},
  onBarPress,
  height = 300,
}) => {
  const { theme } = useAppTheme();
  const { width: screenWidth } = Dimensions.get('window');
  
  // Chart configuration with defaults
  const chartConfig: ChartConfig = {
    height,
    showLegend: true,
    showGrid: true,
    showTooltips: true,
    animationDuration: 300,
    colors: {
      electricity: theme.colors.primary,
      gas: theme.colors.secondary,
      warmth: '#FF6B35',
      tapWater: '#4ECDC4',
      cooling: '#45B7D1',
      redelivery: '#96CEB4',
      produced: '#FFEAA7',
      previousYear: theme.colors.outline,
      temperature: '#FF5722',
      grid: theme.colors.outline,
      text: theme.colors.onSurface,
      background: theme.colors.surface,
    },
    ...config,
  };

  // Chart dimensions
  const padding = { top: 20, right: 40, bottom: 60, left: 60 };
  const chartWidth = screenWidth - 32; // Account for container padding
  const graphWidth = chartWidth - padding.left - padding.right;
  const graphHeight = chartConfig.height - padding.top - padding.bottom;

  // Calculate domain and scales
  const [minValue, maxValue] = useMemo(() => 
    calculateDomain(data, showPreviousYear), [data, showPreviousYear]
  );

  const barWidth = Math.max(8, Math.min(40, graphWidth / (data.length * 1.5)));
  const barSpacing = graphWidth / data.length;

  // Helper functions
  const valueToY = (value: number): number => {
    const ratio = (value - minValue) / (maxValue - minValue);
    return graphHeight - (ratio * graphHeight) + padding.top;
  };

  const getProductValue = (entry: UsageEntry, product: ProductType): number => {
    const productData = entry[product];
    if (!productData) return 0;
    
    return displayMode === 'cost' 
      ? productData.cost 
      : (productData.high + productData.low);
  };

  const getStackedValues = (entry: UsageEntry): Array<{ product: ProductType; value: number; y: number; height: number }> => {
    let cumulativeValue = 0;
    const baseY = valueToY(0);
    
    return selectedProducts.map(product => {
      const value = getProductValue(entry, product);
      const startY = valueToY(cumulativeValue);
      const endY = valueToY(cumulativeValue + value);
      
      cumulativeValue += value;
      
      return {
        product,
        value,
        y: endY,
        height: Math.max(1, startY - endY), // Ensure minimum height for visibility
      };
    }).filter(item => item.value > 0);
  };

  // Y-axis ticks
  const yTicks = useMemo(() => {
    const tickCount = 5;
    const step = (maxValue - minValue) / (tickCount - 1);
    return Array.from({ length: tickCount }, (_, i) => minValue + (step * i));
  }, [minValue, maxValue]);

  const formatYAxisLabel = (value: number): string => {
    if (displayMode === 'cost') {
      return `€${Math.round(value)}`;
    }
    
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}k`;
    }
    
    return Math.round(value).toString();
  };

  const handleBarPress = (entry: UsageEntry, product: ProductType, x: number, y: number) => {
    if (onBarPress) {
      const value = getProductValue(entry, product);
      onBarPress({
        entry,
        product,
        value,
        x,
        y,
      });
    }
  };

  const styles = StyleSheet.create({
    container: {
      backgroundColor: chartConfig.colors.background,
      borderRadius: 8,
      padding: 16,
    },
    chartContainer: {
      height: chartConfig.height,
      width: '100%',
    },
    scrollContainer: {
      flexGrow: 1,
    },
    noDataContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      height: chartConfig.height,
    },
    noDataText: {
      color: theme.colors.onSurfaceVariant,
      fontSize: 16,
    },
  });

  if (data.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>No data available</Text>
        </View>
      </View>
    );
  }

  const shouldScroll = data.length > 10;
  const scrollWidth = shouldScroll ? Math.max(chartWidth, data.length * (barWidth + 8)) : chartWidth;

  return (
    <View style={styles.container}>
      <ScrollView 
        horizontal={shouldScroll}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
      >
        <View style={[styles.chartContainer, { width: scrollWidth }]}>
          <Svg width={scrollWidth} height={chartConfig.height}>
            {/* Grid lines */}
            {chartConfig.showGrid && yTicks.map((tick, index) => (
              <G key={`grid-${index}`}>
                <Line
                  x1={padding.left}
                  y1={valueToY(tick)}
                  x2={scrollWidth - padding.right}
                  y2={valueToY(tick)}
                  stroke={chartConfig.colors.grid}
                  strokeWidth={0.5}
                  strokeDasharray="3,3"
                  opacity={0.5}
                />
                <SvgText
                  x={padding.left - 10}
                  y={valueToY(tick)}
                  fill={chartConfig.colors.text}
                  fontSize={10}
                  textAnchor="end"
                  dy={3}
                >
                  {formatYAxisLabel(tick)}
                </SvgText>
              </G>
            ))}

            {/* X-axis line */}
            <Line
              x1={padding.left}
              y1={graphHeight + padding.top}
              x2={scrollWidth - padding.right}
              y2={graphHeight + padding.top}
              stroke={chartConfig.colors.grid}
              strokeWidth={1}
            />

            {/* Bars */}
            {data.map((entry, entryIndex) => {
              const xPosition = padding.left + (entryIndex * barSpacing) + (barSpacing - barWidth) / 2;
              const stackedValues = getStackedValues(entry);

              return (
                <G key={`entry-${entryIndex}`}>
                  {/* Stacked bars */}
                  {stackedValues.map((stackItem, stackIndex) => (
                    <Rect
                      key={`bar-${entryIndex}-${stackIndex}`}
                      x={xPosition}
                      y={stackItem.y}
                      width={barWidth}
                      height={stackItem.height}
                      fill={chartConfig.colors[stackItem.product]}
                      opacity={0.8}
                      rx={2}
                      onPress={() => handleBarPress(entry, stackItem.product, xPosition + barWidth / 2, stackItem.y)}
                    />
                  ))}

                  {/* Previous year comparison line */}
                  {showPreviousYear && entry.previousYearTotal && (
                    <Line
                      x1={xPosition}
                      y1={valueToY(entry.previousYearTotal)}
                      x2={xPosition + barWidth}
                      y2={valueToY(entry.previousYearTotal)}
                      stroke={chartConfig.colors.previousYear}
                      strokeWidth={2}
                    />
                  )}

                  {/* X-axis labels */}
                  <SvgText
                    x={xPosition + barWidth / 2}
                    y={graphHeight + padding.top + 20}
                    fill={chartConfig.colors.text}
                    fontSize={10}
                    textAnchor="middle"
                    transform={`rotate(${data.length > 6 ? -45 : 0}, ${xPosition + barWidth / 2}, ${graphHeight + padding.top + 20})`}
                  >
                    {formatDateForInterval(entry.date, 'Day')}
                  </SvgText>
                </G>
              );
            })}

            {/* Temperature line overlay */}
            {showTemperature && data.some(entry => entry.temperature !== undefined) && (
              <G>
                {data.map((entry, index) => {
                  if (entry.temperature === undefined || index === 0) return null;
                  
                  const prevEntry = data[index - 1];
                  if (prevEntry.temperature === undefined) return null;

                  const x1 = padding.left + ((index - 1) * barSpacing) + barSpacing / 2;
                  const x2 = padding.left + (index * barSpacing) + barSpacing / 2;
                  
                  // Scale temperature to chart (simple linear scaling)
                  const tempScale = (temp: number) => {
                    const tempMin = -10, tempMax = 40; // Reasonable temperature range
                    const ratio = (temp - tempMin) / (tempMax - tempMin);
                    return graphHeight - (ratio * graphHeight) + padding.top;
                  };

                  return (
                    <Line
                      key={`temp-${index}`}
                      x1={x1}
                      y1={tempScale(prevEntry.temperature)}
                      x2={x2}
                      y2={tempScale(entry.temperature)}
                      stroke={chartConfig.colors.temperature}
                      strokeWidth={2}
                      opacity={0.7}
                    />
                  );
                })}
              </G>
            )}
          </Svg>
        </View>
      </ScrollView>
    </View>
  );
};

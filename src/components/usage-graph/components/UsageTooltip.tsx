/**
 * Usage Tooltip Component
 * Displays detailed information when a bar is tapped
 */

import React from 'react';
import { View, StyleSheet, Modal, TouchableOpacity, Dimensions } from 'react-native';
import { Text } from '@/src/design-system/components/Text';
import { useAppTheme } from '@/src/design-system';
import { TooltipData, UsageEntry, ProductType, DisplayMode } from '../types';
import { formatValue } from '../utils/dataTransformers';

interface UsageTooltipProps {
  tooltip: TooltipData;
  onClose: () => void;
  displayMode: DisplayMode;
}

export const UsageTooltip: React.FC<UsageTooltipProps> = ({
  tooltip,
  onClose,
  displayMode,
}) => {
  const { theme } = useAppTheme();
  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

  if (!tooltip.visible || !tooltip.entry) {
    return null;
  }

  const entry = tooltip.entry;
  const selectedProduct = tooltip.product;

  // Calculate tooltip position
  const tooltipWidth = 280;
  const tooltipHeight = 200;
  
  let tooltipX = tooltip.x - tooltipWidth / 2;
  let tooltipY = tooltip.y - tooltipHeight - 20;

  // Adjust position to stay within screen bounds
  if (tooltipX < 20) tooltipX = 20;
  if (tooltipX + tooltipWidth > screenWidth - 20) tooltipX = screenWidth - tooltipWidth - 20;
  if (tooltipY < 20) tooltipY = tooltip.y + 20;

  const productLabels: Record<ProductType, string> = {
    electricity: 'Electricity',
    gas: 'Gas',
    warmth: 'Heating',
    tapWater: 'Water',
    cooling: 'Cooling',
    redelivery: 'Redelivery',
    produced: 'Solar Production',
  };

  const productColors: Record<ProductType, string> = {
    electricity: theme.colors.primary,
    gas: theme.colors.secondary,
    warmth: '#FF6B35',
    tapWater: '#4ECDC4',
    cooling: '#45B7D1',
    redelivery: '#96CEB4',
    produced: '#FFEAA7',
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getProductData = (product: ProductType) => {
    const data = entry[product];
    if (!data) return null;

    const value = displayMode === 'cost' ? data.cost : (data.high + data.low);
    return {
      label: productLabels[product],
      value: formatValue(value, product, displayMode),
      color: productColors[product],
      status: data.status,
    };
  };

  const allProducts: ProductType[] = ['electricity', 'gas', 'warmth', 'tapWater', 'cooling', 'redelivery', 'produced'];
  const availableProducts = allProducts.filter(product => entry[product]);

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    tooltip: {
      position: 'absolute',
      left: tooltipX,
      top: tooltipY,
      width: tooltipWidth,
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    header: {
      marginBottom: 12,
      paddingBottom: 8,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    date: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 4,
    },
    total: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
    },
    productList: {
      gap: 8,
    },
    productRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 4,
    },
    productInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    colorIndicator: {
      width: 12,
      height: 12,
      borderRadius: 6,
      marginRight: 8,
    },
    productLabel: {
      fontSize: 14,
      color: theme.colors.onSurface,
      flex: 1,
    },
    productValue: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    selectedProduct: {
      backgroundColor: theme.colors.primaryContainer,
      borderRadius: 6,
      paddingHorizontal: 8,
      paddingVertical: 4,
    },
    statusBadge: {
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 4,
      marginLeft: 8,
    },
    statusText: {
      fontSize: 10,
      fontWeight: '500',
    },
    weatherInfo: {
      marginTop: 12,
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    weatherRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 4,
    },
    weatherLabel: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
    },
    weatherValue: {
      fontSize: 12,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    closeButton: {
      position: 'absolute',
      top: 8,
      right: 8,
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: theme.colors.surfaceVariant,
      justifyContent: 'center',
      alignItems: 'center',
    },
    closeText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.onSurfaceVariant,
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'MEASURED': return theme.colors.primary;
      case 'IN_ERROR': return theme.colors.error;
      case 'NOT_MEASURED': return theme.colors.outline;
      default: return theme.colors.outline;
    }
  };

  return (
    <Modal
      visible={tooltip.visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity style={styles.overlay} onPress={onClose} activeOpacity={1}>
        <View style={styles.tooltip}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeText}>×</Text>
          </TouchableOpacity>

          <View style={styles.header}>
            <Text style={styles.date}>{formatDate(entry.date)}</Text>
            <Text style={styles.total}>
              Total: {formatValue(entry.total, 'electricity', displayMode)}
            </Text>
          </View>

          <View style={styles.productList}>
            {availableProducts.map(product => {
              const productData = getProductData(product);
              if (!productData) return null;

              const isSelected = selectedProduct === product;

              return (
                <View
                  key={product}
                  style={[styles.productRow, isSelected && styles.selectedProduct]}
                >
                  <View style={styles.productInfo}>
                    <View
                      style={[
                        styles.colorIndicator,
                        { backgroundColor: productData.color },
                      ]}
                    />
                    <Text style={styles.productLabel}>{productData.label}</Text>
                  </View>
                  <Text style={styles.productValue}>{productData.value}</Text>
                  <View
                    style={[
                      styles.statusBadge,
                      { backgroundColor: getStatusColor(productData.status) },
                    ]}
                  >
                    <Text
                      style={[
                        styles.statusText,
                        { color: theme.colors.onPrimary },
                      ]}
                    >
                      {productData.status}
                    </Text>
                  </View>
                </View>
              );
            })}
          </View>

          {entry.weather && (
            <View style={styles.weatherInfo}>
              <View style={styles.weatherRow}>
                <Text style={styles.weatherLabel}>Temperature:</Text>
                <Text style={styles.weatherValue}>{entry.weather.temp.toFixed(1)}°C</Text>
              </View>
              <View style={styles.weatherRow}>
                <Text style={styles.weatherLabel}>Sunshine:</Text>
                <Text style={styles.weatherValue}>{entry.weather.sunshine.toFixed(1)} hrs</Text>
              </View>
              {entry.weather.windSpeed > 0 && (
                <View style={styles.weatherRow}>
                  <Text style={styles.weatherLabel}>Wind Speed:</Text>
                  <Text style={styles.weatherValue}>{entry.weather.windSpeed.toFixed(1)} m/s</Text>
                </View>
              )}
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

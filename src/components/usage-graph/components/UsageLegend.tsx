/**
 * Usage Legend Component
 * Displays color-coded legend for the usage graph
 */

import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text } from '@/src/design-system/components/Text';
import { useAppTheme } from '@/src/design-system';
import { LegendItem, ProductType } from '../types';

interface UsageLegendProps {
  items: LegendItem[];
  onItemPress?: (product: ProductType) => void;
  horizontal?: boolean;
  showValues?: boolean;
}

export const UsageLegend: React.FC<UsageLegendProps> = ({
  items,
  onItemPress,
  horizontal = true,
  showValues = false,
}) => {
  const { theme } = useAppTheme();

  const styles = StyleSheet.create({
    container: {
      flexDirection: horizontal ? 'row' : 'column',
      flexWrap: horizontal ? 'wrap' : 'nowrap',
      justifyContent: horizontal ? 'center' : 'flex-start',
      alignItems: horizontal ? 'center' : 'stretch',
      gap: horizontal ? 12 : 8,
      paddingVertical: 8,
    },
    item: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 6,
      minHeight: 32,
    },
    itemTouchable: {
      borderWidth: 1,
      borderColor: 'transparent',
    },
    itemPressed: {
      backgroundColor: theme.colors.surfaceVariant,
      borderColor: theme.colors.outline,
    },
    itemHidden: {
      opacity: 0.5,
    },
    colorIndicator: {
      width: 12,
      height: 12,
      borderRadius: 6,
      marginRight: 8,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    labelContainer: {
      flex: 1,
      flexDirection: horizontal ? 'column' : 'row',
      alignItems: horizontal ? 'flex-start' : 'center',
      justifyContent: horizontal ? 'center' : 'space-between',
    },
    label: {
      fontSize: 12,
      fontWeight: '500',
      color: theme.colors.onSurface,
      textAlign: horizontal ? 'center' : 'left',
    },
    value: {
      fontSize: 11,
      color: theme.colors.onSurfaceVariant,
      marginTop: horizontal ? 2 : 0,
      marginLeft: horizontal ? 0 : 8,
    },
    hiddenIndicator: {
      width: 4,
      height: 4,
      borderRadius: 2,
      backgroundColor: theme.colors.outline,
      marginLeft: 4,
    },
  });

  const handleItemPress = (item: LegendItem) => {
    if (onItemPress) {
      // Extract product type from label (this is a simple approach)
      const productMap: Record<string, ProductType> = {
        'Electricity': 'electricity',
        'Gas': 'gas',
        'Heating': 'warmth',
        'Water': 'tapWater',
        'Cooling': 'cooling',
        'Redelivery': 'redelivery',
        'Solar Production': 'produced',
      };
      
      const product = productMap[item.label];
      if (product) {
        onItemPress(product);
      }
    }
  };

  return (
    <View style={styles.container}>
      {items.map((item, index) => {
        const ItemComponent = onItemPress ? TouchableOpacity : View;
        
        return (
          <ItemComponent
            key={`${item.label}-${index}`}
            style={[
              styles.item,
              onItemPress && styles.itemTouchable,
              !item.visible && styles.itemHidden,
            ]}
            onPress={onItemPress ? () => handleItemPress(item) : undefined}
            activeOpacity={0.7}
            accessibilityRole={onItemPress ? "button" : undefined}
            accessibilityLabel={`${item.label}${showValues && item.value ? `: ${item.value} ${item.unit || ''}` : ''}`}
            accessibilityState={onItemPress ? { selected: item.visible } : undefined}
          >
            <View
              style={[
                styles.colorIndicator,
                { backgroundColor: item.visible ? item.color : 'transparent' },
              ]}
            />
            
            <View style={styles.labelContainer}>
              <Text style={styles.label} numberOfLines={1}>
                {item.label}
              </Text>
              
              {showValues && item.value !== undefined && (
                <Text style={styles.value} numberOfLines={1}>
                  {item.value} {item.unit || ''}
                </Text>
              )}
            </View>
            
            {!item.visible && <View style={styles.hiddenIndicator} />}
          </ItemComponent>
        );
      })}
    </View>
  );
};

// Helper function to create legend items from usage data
export const createLegendItems = (
  availableProducts: ProductType[],
  visibleProducts: ProductType[],
  colors: Record<ProductType, string>,
  values?: Record<ProductType, { value: number; unit: string }>
): LegendItem[] => {
  const productLabels: Record<ProductType, string> = {
    electricity: 'Electricity',
    gas: 'Gas',
    warmth: 'Heating',
    tapWater: 'Water',
    cooling: 'Cooling',
    redelivery: 'Redelivery',
    produced: 'Solar Production',
  };

  return availableProducts.map(product => ({
    label: productLabels[product],
    color: colors[product],
    visible: visibleProducts.includes(product),
    value: values?.[product]?.value,
    unit: values?.[product]?.unit,
  }));
};

// Default color scheme for products
export const defaultProductColors: Record<ProductType, string> = {
  electricity: '#2196F3',  // Blue
  gas: '#FF9800',          // Orange
  warmth: '#F44336',       // Red
  tapWater: '#00BCD4',     // Cyan
  cooling: '#3F51B5',      // Indigo
  redelivery: '#4CAF50',   // Green
  produced: '#FFEB3B',     // Yellow
};

// Helper to get contrasting text color
export const getContrastColor = (backgroundColor: string): string => {
  // Simple contrast calculation
  const hex = backgroundColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 128 ? '#000000' : '#FFFFFF';
};

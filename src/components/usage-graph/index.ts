/**
 * Usage Graph Component Library
 * Export all components and utilities
 */

// Main components
export { UsageGraph, defaultUsageGraphConfig, createInitialFilters, UsageGraphErrorBoundary } from './components/UsageGraph';
export { UsageChart } from './components/UsageChart';
export { UsageFilters } from './components/UsageFilters';
export { UsageLegend, createLegendItems, defaultProductColors } from './components/UsageLegend';
export { UsageTooltip } from './components/UsageTooltip';
export { LoadingSkeleton } from './components/LoadingSkeleton';

// Types
export type {
  ProductType,
  TimeInterval,
  DisplayMode,
  UsageEntry,
  FilterConfig,
  ChartConfig,
  TouchEventData,
  TooltipData,
  LegendItem,
  NavigationControls,
  ValueFormatter,
  DateFormatter,
  AnimationConfig,
  ErrorBoundaryProps,
  UsageGraphProps,
  UsageFiltersProps,
} from './types';

// Utilities
export {
  safeDecimalValue,
  transformUsageEntries,
  filterByProducts,
  getAvailableProducts,
  calculateDomain,
  formatDateForInterval,
  formatValue,
} from './utils/dataTransformers';

// Re-export API types for convenience
export type {
  Usages_V3_Aggregation_Interval_UsagesEntry,
  DecimalObject,
  Usages_V3_Aggregation_Interval_UsageItem,
} from '@/src/api/types';

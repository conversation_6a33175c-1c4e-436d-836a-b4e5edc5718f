/**
 * Types for the Usage Graph Component Library
 */

import { Usages_V3_Aggregation_Interval_UsagesEntry, DecimalObject } from '@/src/api/types';

// Product types that can be displayed
export type ProductType = 'electricity' | 'gas' | 'warmth' | 'tapWater' | 'cooling' | 'redelivery' | 'produced';

// Time interval options
export type TimeInterval = 'Hour' | 'Day' | 'Week' | 'Month' | 'Year';

// Display mode for values
export type DisplayMode = 'consumption' | 'cost';

// Transformed usage entry for chart display
export interface UsageEntry {
  date: string;
  total: number;
  max: number;
  electricity?: {
    high: number;
    low: number;
    cost: number;
    status: string;
  };
  gas?: {
    high: number;
    low: number;
    cost: number;
    status: string;
  };
  warmth?: {
    high: number;
    low: number;
    cost: number;
    status: string;
  };
  tapWater?: {
    high: number;
    low: number;
    cost: number;
    status: string;
  };
  cooling?: {
    high: number;
    low: number;
    cost: number;
    status: string;
  };
  redelivery?: {
    high: number;
    low: number;
    cost: number;
    status: string;
  };
  produced?: {
    high: number;
    low: number;
    cost: number;
    status: string;
  };
  previousYearTotal?: number;
  temperature?: number;
  sunshine?: number;
  isProjected?: boolean;
  weather?: {
    temp: number;
    sunshine: number;
    windSpeed: number;
  };
}

// Filter configuration
export interface FilterConfig {
  products: ProductType[];
  timeInterval: TimeInterval;
  displayMode: DisplayMode;
  showPreviousYear: boolean;
  showTemperature: boolean;
  showProjected: boolean;
}

// Chart configuration
export interface ChartConfig {
  height: number;
  showLegend: boolean;
  showGrid: boolean;
  showTooltips: boolean;
  animationDuration: number;
  colors: {
    electricity: string;
    gas: string;
    warmth: string;
    tapWater: string;
    cooling: string;
    redelivery: string;
    produced: string;
    previousYear: string;
    temperature: string;
    grid: string;
    text: string;
    background: string;
  };
}

// Touch event data
export interface TouchEventData {
  entry: UsageEntry;
  product: ProductType;
  value: number;
  x: number;
  y: number;
}

// Component props
export interface UsageGraphProps {
  data: Usages_V3_Aggregation_Interval_UsagesEntry[];
  config?: Partial<ChartConfig>;
  onBarPress?: (event: TouchEventData) => void;
  onFilterChange?: (filters: FilterConfig) => void;
  loading?: boolean;
  error?: string | null;
  height?: number;
}

export interface UsageFiltersProps {
  filters: FilterConfig;
  onFiltersChange: (filters: FilterConfig) => void;
  availableProducts: ProductType[];
  disabled?: boolean;
}

// Tooltip/Modal data
export interface TooltipData {
  visible: boolean;
  x: number;
  y: number;
  entry: UsageEntry | null;
  product: ProductType | null;
}

// Legend item
export interface LegendItem {
  label: string;
  color: string;
  value?: number;
  unit?: string;
  visible: boolean;
}

// Navigation controls
export interface NavigationControls {
  canGoBack: boolean;
  canGoForward: boolean;
  currentPeriod: string;
  onPrevious: () => void;
  onNext: () => void;
}

// Helper function types
export type ValueFormatter = (value: number, unit?: string) => string;
export type DateFormatter = (date: string, interval: TimeInterval) => string;

// Animation configuration
export interface AnimationConfig {
  duration: number;
  easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
}

// Error boundary props
export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error }>;
  onError?: (error: Error, errorInfo: any) => void;
}

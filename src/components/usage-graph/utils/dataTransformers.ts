/**
 * Data transformation utilities for Usage Graph components
 */

import {
    DecimalObject,
    Usages_V3_Aggregation_Interval_UsageItem,
    Usages_V3_Aggregation_Interval_UsagesEntry
} from '@/src/api/types';
import { DisplayMode, ProductType, TimeInterval, UsageEntry } from '../types';

/**
 * Safely extract decimal value
 */
export const safeDecimalValue = (decimal?: DecimalObject): number => {
  if (!decimal || typeof decimal !== 'object') {
    return 0;
  }
  
  const value = decimal.value;
  
  if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
    return value;
  }
  
  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    if (!isNaN(parsed) && isFinite(parsed)) {
      return parsed;
    }
  }
  
  return 0;
};

/**
 * Transform usage item to simplified format
 */
const transformUsageItem = (item?: Usages_V3_Aggregation_Interval_UsageItem, displayMode: DisplayMode = 'consumption') => {
  if (!item) return undefined;
  
  const getValue = (high?: DecimalObject, low?: DecimalObject) => {
    return safeDecimalValue(high) + safeDecimalValue(low);
  };
  
  const getCost = (item: Usages_V3_Aggregation_Interval_UsageItem) => {
    return safeDecimalValue(item.totalCostInclVat);
  };
  
  return {
    high: safeDecimalValue(item.high),
    low: safeDecimalValue(item.low),
    cost: getCost(item),
    status: item.status || 'MEASURED',
  };
};

/**
 * Transform Digital Core usage entries to chart format
 */
export const transformUsageEntries = (
  entries: Usages_V3_Aggregation_Interval_UsagesEntry[],
  displayMode: DisplayMode = 'consumption'
): UsageEntry[] => {
  console.log('transformUsageEntries called with:', entries.length, 'entries');
  console.log('First entry:', entries[0]);

  return entries.map(entry => {
    const actual = entry.actual;
    const previousYear = entry.previousYear;
    const weather = entry.weather;

    console.log('Processing entry:', { actual, previousYear, weather });
    
    // Transform each product type
    const electricity = transformUsageItem(actual?.electricity, displayMode);
    const gas = transformUsageItem(actual?.gas, displayMode);
    const warmth = transformUsageItem(actual?.warmth, displayMode);
    const tapWater = transformUsageItem(actual?.tapWater, displayMode);
    const cooling = transformUsageItem(actual?.cooling, displayMode);
    const redelivery = transformUsageItem(actual?.redelivery, displayMode);
    const produced = transformUsageItem(actual?.produced, displayMode);
    
    // Calculate total based on display mode
    const getTotal = () => {
      if (displayMode === 'cost') {
        return safeDecimalValue(actual?.totalCostInclVat);
      } else {
        // Sum all consumption values
        return [electricity, gas, warmth, tapWater, cooling, redelivery, produced]
          .filter(Boolean)
          .reduce((sum, item) => sum + (item?.high || 0) + (item?.low || 0), 0);
      }
    };
    
    const total = getTotal();
    
    // Calculate max for scaling
    const max = Math.max(
      total,
      previousYear ? safeDecimalValue(previousYear.totalCostInclVat) : 0
    );
    
    return {
      date: actual?.date || '',
      total,
      max,
      electricity,
      gas,
      warmth,
      tapWater,
      cooling,
      redelivery,
      produced,
      previousYearTotal: previousYear ? safeDecimalValue(previousYear.totalCostInclVat) : undefined,
      temperature: weather ? safeDecimalValue(weather.temp) : undefined,
      sunshine: weather ? safeDecimalValue(weather.sunshine) : undefined,
      weather: weather ? {
        temp: safeDecimalValue(weather.temp),
        sunshine: safeDecimalValue(weather.sunshine),
        windSpeed: safeDecimalValue(weather.windSpeed),
      } : undefined,
      isProjected: false, // This would need to be determined from the data source
    };
  });
};

/**
 * Filter entries by selected products
 */
export const filterByProducts = (
  entries: UsageEntry[],
  selectedProducts: ProductType[]
): UsageEntry[] => {
  return entries.map(entry => {
    const filtered: UsageEntry = {
      ...entry,
      total: 0,
    };
    
    // Only include selected products
    selectedProducts.forEach(product => {
      if (entry[product]) {
        filtered[product] = entry[product];
        if (entry[product]) {
          filtered.total += (entry[product]!.high + entry[product]!.low);
        }
      }
    });
    
    return filtered;
  });
};

/**
 * Get available products from data
 */
export const getAvailableProducts = (entries: UsageEntry[]): ProductType[] => {
  const products = new Set<ProductType>();
  
  entries.forEach(entry => {
    (['electricity', 'gas', 'warmth', 'tapWater', 'cooling', 'redelivery', 'produced'] as ProductType[])
      .forEach(product => {
        if (entry[product] && (entry[product]!.high > 0 || entry[product]!.low > 0)) {
          products.add(product);
        }
      });
  });
  
  return Array.from(products);
};

/**
 * Calculate chart domain (min/max values)
 */
export const calculateDomain = (entries: UsageEntry[], showPreviousYear: boolean = false): [number, number] => {
  if (entries.length === 0) {
    return [0, 100];
  }
  
  const values = entries.flatMap(entry => {
    const values = [entry.total];
    if (showPreviousYear && entry.previousYearTotal) {
      values.push(entry.previousYearTotal);
    }
    return values;
  }).filter(value => value > 0);
  
  if (values.length === 0) {
    return [0, 100];
  }
  
  const max = Math.max(...values);
  const min = Math.min(...values);
  
  // Add 10% padding
  const padding = (max - min) * 0.1;
  
  return [
    Math.max(0, min - padding),
    max + padding
  ];
};

/**
 * Format date based on interval
 */
export const formatDateForInterval = (date: string, interval: TimeInterval): string => {
  const dateObj = new Date(date);
  
  switch (interval) {
    case 'Hour':
      return dateObj.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
    case 'Day':
      return dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    case 'Week':
      return `Week ${Math.ceil(dateObj.getDate() / 7)}`;
    case 'Month':
      return dateObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    case 'Year':
      return dateObj.getFullYear().toString();
    default:
      return date;
  }
};

/**
 * Format value with appropriate units
 */
export const formatValue = (value: number, product: ProductType, displayMode: DisplayMode): string => {
  if (displayMode === 'cost') {
    return `€${value.toFixed(2)}`;
  }
  
  const units: Record<ProductType, string> = {
    electricity: 'kWh',
    gas: 'm³',
    warmth: 'kWh',
    tapWater: 'L',
    cooling: 'kWh',
    redelivery: 'kWh',
    produced: 'kWh',
  };
  
  const unit = units[product] || '';
  
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}k ${unit}`;
  }
  
  return `${value.toFixed(1)} ${unit}`;
};

/**
 * Digital Core Dashboard Component
 *
 * This component combines all the Digital Core API components into a single dashboard.
 */

import React, { useEffect, useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { useDigitalCore } from '../api/hooks/useDigitalCore';
import { ProfileResponse } from '../api/types';
import { tokens } from '../design-system';
import { Heading } from '../design-system/components';
import { EnergyUsage } from './EnergyUsage';
import { ProfileInfo } from './ProfileInfo';

/**
 * Digital Core Dashboard Component
 */
export const DCDashboard: React.FC = () => {
  const { getProfile } = useDigitalCore();
  const [profile, setProfile] = useState<ProfileResponse | null>(null);
  const [selectedAccountId, setSelectedAccountId] = useState<number | null>(null);

  // Fetch profile on mount to get account ID
  useEffect(() => {
    const fetchProfile = async () => {
      const profileData = await getProfile();
      if (profileData) {
        setProfile(profileData);

        // Set the first active account as the selected account
        const activeAccount = profileData.data.accounts.find((account: any) => account.active);
        if (activeAccount) {
          setSelectedAccountId(activeAccount.accountId);
        }
      }
    };

    fetchProfile();
  }, [getProfile]);

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Heading as="h1" size="M" style={styles.heading}>
          Digital Core Dashboard
        </Heading>

        {/* Profile Information */}
        <ProfileInfo />

        {/* Energy Usage (only shown if an account is selected) */}
        {selectedAccountId && (
          <EnergyUsage accountId={selectedAccountId} />
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: tokens.colors.backgroundColors.backgroundSecondary,
  },
  content: {
    padding: tokens.spacing.space[4],
  },
  heading: {
    marginBottom: tokens.spacing.space[4],
  },
});

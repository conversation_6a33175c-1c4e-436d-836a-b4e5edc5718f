/**
 * Energy Usage Chart Component using Victory.js and React Native SVG
 *
 * This component uses different implementations for web and native platforms:
 * - Web: Uses the 'victory' package
 * - Android: Uses 'victory-native' package
 * - iOS: Uses a custom SVG implementation due to Skia module issues
 */

import React, { useState } from 'react';
import { Platform, StyleSheet, View, useWindowDimensions } from 'react-native';
import Svg, {
    G,
    Line,
    Rect,
    Text as SvgText
} from 'react-native-svg';
import { Text } from '../../../../src/design-system/components';
import { useAppTheme } from '../../../../src/design-system/provider';
import { ChartDataPoint, DisplayMode } from '../../types/api';
import { formatCurrency, formatNumber } from '../../utils/formatters';

// Import Victory components based on platform
let VictoryAxis, VictoryBar, VictoryChart, VictoryGroup, VictoryStack, VictoryTheme, VictoryTooltip;

// Check if we're on web
const isWeb = Platform.OS === 'web';
const isIOS = Platform.OS === 'ios';

// Dynamic imports based on platform
if (isWeb) {
  // Web implementation
  const Victory = require('victory');
  VictoryAxis = Victory.VictoryAxis;
  VictoryBar = Victory.VictoryBar;
  VictoryChart = Victory.VictoryChart;
  VictoryGroup = Victory.VictoryGroup;
  VictoryStack = Victory.VictoryStack;
  VictoryTheme = Victory.VictoryTheme;
  VictoryTooltip = Victory.VictoryTooltip;
} else if (!isIOS) {
  try {
    // Try to use victory-native (works on Android)
    const Victory = require('victory-native');
    VictoryAxis = Victory.VictoryAxis;
    VictoryBar = Victory.VictoryBar;
    VictoryChart = Victory.VictoryChart;
    VictoryGroup = Victory.VictoryGroup;
    VictoryStack = Victory.VictoryStack;
    VictoryTheme = Victory.VictoryTheme;
    VictoryTooltip = Victory.VictoryTooltip;
  } catch (error) {
    // Fallback for other platforms if victory-native fails
    console.log('Victory-native not available, using fallback implementation');
    // Define empty components that will be replaced with our custom implementation
    VictoryAxis = () => null;
    VictoryBar = () => null;
    VictoryChart = (_props: { children?: React.ReactNode }) => null;
    VictoryGroup = (_props: { children?: React.ReactNode }) => null;
    VictoryStack = (_props: { children?: React.ReactNode }) => null;
    VictoryTheme = { material: {} };
    VictoryTooltip = () => null;
  }
} else {
  // iOS - we'll use our custom SVG implementation
  // Define empty components as placeholders
  VictoryAxis = () => null;
  VictoryBar = () => null;
  VictoryChart = (_props: { children?: React.ReactNode }) => null;
  VictoryGroup = (_props: { children?: React.ReactNode }) => null;
  VictoryStack = (_props: { children?: React.ReactNode }) => null;
  VictoryTheme = { material: {} };
  VictoryTooltip = () => null;
}

interface EnergyChartProps {
  data: ChartDataPoint[];
  displayMode: DisplayMode;
  showComparison?: boolean;
}

/**
 * Energy Usage Chart Component
 * Displays a stacked bar chart showing electricity and gas usage using Victory.js
 */
export const EnergyUsageChart: React.FC<EnergyChartProps> = ({
  data,
  displayMode,
  showComparison = false,
}) => {
  const { theme } = useAppTheme();
  const { width } = useWindowDimensions();

  console.log('EnergyUsageChart received data:', data);
  console.log('Data length:', data?.length);
  console.log('Display mode:', displayMode);
  console.log('Show comparison:', showComparison);

  // Determine which data keys to use based on display mode
  const electricityKey = displayMode === 'cost' ? 'electricityCost' : 'electricity';
  const gasKey = displayMode === 'cost' ? 'gasCost' : 'gas';
  const prevYearElectricityKey = displayMode === 'cost' ? 'previousYearElectricityCost' : 'previousYearElectricity';
  const prevYearGasKey = displayMode === 'cost' ? 'previousYearGasCost' : 'previousYearGas';

  // Format Y-axis ticks
  const formatYAxis = (value: number) => {
    if (displayMode === 'cost') {
      return `€${value}`;
    } else {
      return value.toString();
    }
  };

  // Format tooltip labels
  const formatTooltipLabel = (datum: any) => {
    const value = datum.y;
    const name = datum._stack ? datum._stack[0] : '';

    if (displayMode === 'cost') {
      return `${formatCurrency(value)}\n${name === 'electricity' ? 'Electricity' : 'Gas'}`;
    } else {
      const unit = name === 'electricity' ? 'kWh' : 'm³';
      return `${formatNumber(value)} ${unit}\n${name === 'electricity' ? 'Electricity' : 'Gas'}`;
    }
  };

  // Helper function to safely get numeric value
  const safeValue = (value: any): number => {
    if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
      return value;
    }
    return 0;
  };

  // Transform data for Victory.js
  const transformDataForVictory = () => {
    // Current year data
    const electricityData = data.map(item => ({
      x: item.name,
      y: safeValue(item[electricityKey]),
      _stack: ['electricity', 'current'],
      label: safeValue(item[electricityKey]) > 0 ? formatTooltipLabel({ y: safeValue(item[electricityKey]), _stack: ['electricity'] }) : '',
    }));

    const gasData = data.map(item => ({
      x: item.name,
      y: safeValue(item[gasKey]),
      _stack: ['gas', 'current'],
      label: safeValue(item[gasKey]) > 0 ? formatTooltipLabel({ y: safeValue(item[gasKey]), _stack: ['gas'] }) : '',
    }));

    // Previous year data (if comparison is enabled)
    const prevYearElectricityData = showComparison ? data.map(item => ({
      x: item.name,
      y: safeValue(item[prevYearElectricityKey]),
      _stack: ['electricity', 'previous'],
      label: safeValue(item[prevYearElectricityKey]) > 0 ? formatTooltipLabel({ y: safeValue(item[prevYearElectricityKey]), _stack: ['electricity'] }) : '',
    })) : [];

    const prevYearGasData = showComparison ? data.map(item => ({
      x: item.name,
      y: safeValue(item[prevYearGasKey]),
      _stack: ['gas', 'previous'],
      label: safeValue(item[prevYearGasKey]) > 0 ? formatTooltipLabel({ y: safeValue(item[prevYearGasKey]), _stack: ['gas'] }) : '',
    })) : [];

    return {
      electricityData,
      gasData,
      prevYearElectricityData,
      prevYearGasData
    };
  };

  const { electricityData, gasData, prevYearElectricityData, prevYearGasData } = transformDataForVictory();

  // Calculate domain for Y axis
  const calculateYDomain = () => {
    let maxValue = 0;

    data.forEach(item => {
      const currentYearSum = safeValue(item[electricityKey]) + safeValue(item[gasKey]);
      const prevYearSum = showComparison ?
        safeValue(item[prevYearElectricityKey]) + safeValue(item[prevYearGasKey]) : 0;

      maxValue = Math.max(maxValue, currentYearSum, prevYearSum);
    });

    // Add 10% padding to the top, ensure minimum value
    const domainMax = Math.max(maxValue * 1.1, 10); // Minimum domain of 10
    return [0, isFinite(domainMax) ? domainMax : 100];
  };

  // Chart theme based on app theme
  const chartTheme = {
    axis: {
      style: {
        axis: {
          stroke: theme.colors.outline,
        },
        tickLabels: {
          fill: theme.colors.onSurface,
          fontSize: 10,
        },
        grid: {
          stroke: theme.colors.outline,
          strokeDasharray: '3,3',
          strokeOpacity: 0.5,
        },
      },
    },
  };

  // Check if we're on iOS and need to use the SVG implementation
  const isIOS = Platform.OS === 'ios';
  const useNativeSVG = !isWeb && isIOS;

  // State for tooltip in the main component
  const [activeTooltip, setActiveTooltip] = useState<{
    x: number;
    y: number;
    value: string;
    type: string;
  } | null>(null);

  // Custom SVG chart implementation for iOS
  const renderIOSSVGChart = () => {
    // Chart dimensions and spacing
    const chartHeight = 250;
    const chartWidth = width - 32; // Adjust for container padding
    const padding = { top: 20, right: 40, bottom: 50, left: 60 };
    const graphWidth = chartWidth - padding.left - padding.right;
    const graphHeight = chartHeight - padding.top - padding.bottom;
    const barWidth = 15;

    // Calculate max value for Y axis scaling
    const maxValue = calculateYDomain()[1];

    // Calculate bar positions and dimensions
    const barSpacing = graphWidth / (data.length * 2);

    // Transform data for SVG rendering
    const { electricityData, gasData, prevYearElectricityData, prevYearGasData } = transformDataForVictory();

    // Y-axis tick values - ensure maxValue is safe
    const safeMaxValue = Math.max(maxValue, 1);
    const yTicks = [0, safeMaxValue * 0.25, safeMaxValue * 0.5, safeMaxValue * 0.75, safeMaxValue];

    // Format Y-axis labels
    const formatYLabel = (value: number) => {
      const safeValue = isFinite(value) ? value : 0;
      if (displayMode === 'cost') {
        return `€${Math.round(safeValue)}`;
      } else {
        return Math.round(safeValue).toString();
      }
    };

    // Helper to convert data value to Y position
    const valueToY = (value: number) => {
      const safeVal = isFinite(value) ? value : 0;
      return graphHeight - (safeVal / safeMaxValue) * graphHeight + padding.top;
    };

    // Render the bars for a data point
    const renderBars = (index: number) => {
      const electricityValue = safeValue(electricityData[index]?.y);
      const gasValue = safeValue(gasData[index]?.y);
      const prevElectricityValue = showComparison ? safeValue(prevYearElectricityData[index]?.y) : 0;
      const prevGasValue = showComparison ? safeValue(prevYearGasData[index]?.y) : 0;

      const xPosition = padding.left + barSpacing + index * (barSpacing * 2);
      const comparisonXPosition = xPosition + barWidth + 5;

      // Ensure maxValue is not zero to prevent division by zero
      const safeMaxValue = Math.max(maxValue, 1);

      const electricityHeight = (electricityValue / safeMaxValue) * graphHeight;
      const gasHeight = (gasValue / safeMaxValue) * graphHeight;
      const prevElectricityHeight = (prevElectricityValue / safeMaxValue) * graphHeight;
      const prevGasHeight = (prevGasValue / safeMaxValue) * graphHeight;

      const electricityY = graphHeight - electricityHeight + padding.top;
      const gasY = graphHeight - gasHeight + padding.top;
      const prevElectricityY = graphHeight - prevElectricityHeight + padding.top;
      const prevGasY = graphHeight - prevGasHeight + padding.top;

      return (
        <G key={`bars-${index}`}>
          {/* Current year electricity */}
          <Rect
            x={xPosition}
            y={electricityY}
            width={barWidth}
            height={electricityHeight}
            fill={theme.colors.primary}
            onPress={() => {
              const value = displayMode === 'cost'
                ? formatCurrency(electricityValue)
                : `${formatNumber(electricityValue)} kWh`;
              setActiveTooltip({
                x: xPosition + barWidth / 2,
                y: electricityY,
                value,
                type: 'Electricity'
              });
            }}
          />

          {/* Current year gas */}
          <Rect
            x={xPosition}
            y={gasY}
            width={barWidth}
            height={gasHeight}
            fill={theme.colors.tertiary}
            onPress={() => {
              const value = displayMode === 'cost'
                ? formatCurrency(gasValue)
                : `${formatNumber(gasValue)} m³`;
              setActiveTooltip({
                x: xPosition + barWidth / 2,
                y: gasY,
                value,
                type: 'Gas'
              });
            }}
          />

          {/* Previous year comparison if enabled */}
          {showComparison && (
            <>
              <Rect
                x={comparisonXPosition}
                y={prevElectricityY}
                width={barWidth}
                height={prevElectricityHeight}
                fill={`${theme.colors.primary}80`}
                onPress={() => {
                  const value = displayMode === 'cost'
                    ? formatCurrency(prevElectricityValue)
                    : `${formatNumber(prevElectricityValue)} kWh`;
                  setActiveTooltip({
                    x: comparisonXPosition + barWidth / 2,
                    y: prevElectricityY,
                    value,
                    type: 'Last Year Electricity'
                  });
                }}
              />

              <Rect
                x={comparisonXPosition}
                y={prevGasY}
                width={barWidth}
                height={prevGasHeight}
                fill={`${theme.colors.tertiary}80`}
                onPress={() => {
                  const value = displayMode === 'cost'
                    ? formatCurrency(prevGasValue)
                    : `${formatNumber(prevGasValue)} m³`;
                  setActiveTooltip({
                    x: comparisonXPosition + barWidth / 2,
                    y: prevGasY,
                    value,
                    type: 'Last Year Gas'
                  });
                }}
              />
            </>
          )}
        </G>
      );
    };

    return (
      <View style={{ height: chartHeight, width: '100%' }}>
        <Svg width={chartWidth} height={chartHeight}>
          {/* Y-axis grid lines */}
          {yTicks.map((tick, index) => (
            <G key={`grid-${index}`}>
              <Line
                x1={padding.left}
                y1={valueToY(tick)}
                x2={chartWidth - padding.right}
                y2={valueToY(tick)}
                stroke={theme.colors.outline}
                strokeWidth={1}
                strokeDasharray="3,3"
                strokeOpacity={0.5}
              />
              <SvgText
                x={padding.left - 10}
                y={valueToY(tick)}
                fill={theme.colors.onSurface}
                textAnchor="end"
                fontSize={10}
                dy={3} // Vertical alignment
              >
                {formatYLabel(tick)}
              </SvgText>
            </G>
          ))}

          {/* X-axis line */}
          <Line
            x1={padding.left}
            y1={graphHeight + padding.top}
            x2={chartWidth - padding.right}
            y2={graphHeight + padding.top}
            stroke={theme.colors.outline}
            strokeWidth={1}
          />

          {/* X-axis labels */}
          {data.map((item, index) => (
            <SvgText
              key={`label-${index}`}
              x={padding.left + barSpacing + index * (barSpacing * 2) + barWidth / 2}
              y={graphHeight + padding.top + 20}
              fill={theme.colors.onSurface}
              textAnchor="middle"
              fontSize={10}
            >
              {item.name}
            </SvgText>
          ))}

          {/* Render all bars */}
          {data.map((_, index) => renderBars(index))}

          {/* Tooltip */}
          {activeTooltip && (
            <G>
              <Rect
                x={activeTooltip.x - 40}
                y={activeTooltip.y - 40}
                width={80}
                height={35}
                fill={theme.colors.surface}
                stroke={theme.colors.outline}
                strokeWidth={1}
                rx={4}
                ry={4}
              />
              <SvgText
                x={activeTooltip.x}
                y={activeTooltip.y - 25}
                fill={theme.colors.onSurface}
                textAnchor="middle"
                fontSize={10}
              >
                {activeTooltip.value}
              </SvgText>
              <SvgText
                x={activeTooltip.x}
                y={activeTooltip.y - 10}
                fill={theme.colors.onSurface}
                textAnchor="middle"
                fontSize={10}
              >
                {activeTooltip.type}
              </SvgText>
            </G>
          )}
        </Svg>
      </View>
    );
  };

  // Add a check for empty data
  if (!data || data.length === 0) {
    console.log('EnergyUsageChart: No data to display');
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center', padding: 20 }]}>
        <Text style={{ color: theme.colors.onSurface, marginBottom: 10 }}>No data available</Text>
        <Text style={{ color: theme.colors.onSurface, fontSize: 12 }}>
          Data received: {data ? `Array with ${data.length} items` : 'null/undefined'}
        </Text>
        <Text style={{ color: theme.colors.onSurface, fontSize: 12 }}>
          Display mode: {displayMode}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {useNativeSVG ? (
        renderIOSSVGChart()
      ) : (
        <View style={{ height: 250, width: '100%' }}>
          <VictoryChart
            theme={chartTheme}
            domainPadding={{ x: 25 }}
            padding={{ top: 20, right: 40, bottom: 50, left: 60 }}
            height={250}
            width={width - 32} // Adjust for container padding
            containerComponent={
              <VictoryGroup responsive={false} />
            }
          >
            {/* Grid lines */}
            <VictoryAxis
              dependentAxis
              tickFormat={formatYAxis}
              style={{
                grid: { stroke: theme.colors.outline, strokeDasharray: '3,3', strokeOpacity: 0.5 },
                tickLabels: { fontSize: 10, fill: theme.colors.onSurface },
                axis: { stroke: theme.colors.outline },
              }}
              domain={calculateYDomain()}
            />

            {/* X Axis */}
            <VictoryAxis
              style={{
                tickLabels: { fontSize: 10, fill: theme.colors.onSurface, angle: data.length > 6 ? -45 : 0 },
                axis: { stroke: theme.colors.outline },
              }}
            />

            {/* Current year data */}
            <VictoryGroup offset={20} style={{ data: { width: 15 } }}>
              <VictoryStack>
                <VictoryBar
                  data={electricityData}
                  style={{ data: { fill: theme.colors.primary } }}
                  labelComponent={<VictoryTooltip
                    flyoutStyle={{
                      fill: theme.colors.surface,
                      stroke: theme.colors.outline,
                    }}
                    style={{ fill: theme.colors.onSurface, fontSize: 10 }}
                  />}
                />
                <VictoryBar
                  data={gasData}
                  style={{ data: { fill: theme.colors.tertiary } }}
                  labelComponent={<VictoryTooltip
                    flyoutStyle={{
                      fill: theme.colors.surface,
                      stroke: theme.colors.outline,
                    }}
                    style={{ fill: theme.colors.onSurface, fontSize: 10 }}
                  />}
                />
              </VictoryStack>

              {/* Previous year comparison if enabled */}
              {showComparison && (
                <VictoryStack>
                  <VictoryBar
                    data={prevYearElectricityData}
                    style={{ data: { fill: `${theme.colors.primary}80` } }}
                    labelComponent={<VictoryTooltip
                      flyoutStyle={{
                        fill: theme.colors.surface,
                        stroke: theme.colors.outline,
                      }}
                      style={{ fill: theme.colors.onSurface, fontSize: 10 }}
                    />}
                  />
                  <VictoryBar
                    data={prevYearGasData}
                    style={{ data: { fill: `${theme.colors.tertiary}80` } }}
                    labelComponent={<VictoryTooltip
                      flyoutStyle={{
                        fill: theme.colors.surface,
                        stroke: theme.colors.outline,
                      }}
                      style={{ fill: theme.colors.onSurface, fontSize: 10 }}
                    />}
                  />
                </VictoryStack>
              )}
            </VictoryGroup>
          </VictoryChart>
        </View>
      )}

      {/* Custom Legend */}
      <View style={styles.legendContainer}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: theme.colors.primary }]} />
          <Text style={{ color: theme.colors.onSurface }}>Electricity</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: theme.colors.tertiary }]} />
          <Text style={{ color: theme.colors.onSurface }}>Gas</Text>
        </View>
        {showComparison && (
          <>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: `${theme.colors.primary}80` }]} />
              <Text style={{ color: theme.colors.onSurface }}>Last Year Electricity</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: `${theme.colors.tertiary}80` }]} />
              <Text style={{ color: theme.colors.onSurface }}>Last Year Gas</Text>
            </View>
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 300, // Increased height to accommodate legend
    width: '100%',
  },
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginTop: -20,
    paddingHorizontal: 10,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    marginRight: 4,
    borderRadius: 2,
  },
  // SVG Chart styles
  svgChartContainer: {
    height: 250,
    width: '100%',
    backgroundColor: 'transparent',
  },
  tooltipContainer: {
    position: 'absolute',
    backgroundColor: 'white',
    padding: 8,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ccc',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  tooltipText: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default EnergyUsageChart;
